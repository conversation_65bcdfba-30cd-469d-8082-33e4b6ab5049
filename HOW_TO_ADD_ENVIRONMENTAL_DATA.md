# How to Add Your Environmental Data

## 🎯 **Quick Start**

You now have a **working template** that you can easily edit with your physicochemical data!

## 📁 **What You Have**

✅ **`environmental_data_template.csv`** - Template with example data  
✅ **`environmental_correlation_analysis.R`** - Analysis script (ready to run)  
✅ **`ENVIRONMENTAL_ANALYSIS_GUIDE.md`** - Comprehensive guide  

## 🔧 **How to Add Your Data**

### **Option 1: Edit CSV File (Easiest)**
1. **Open** `environmental_data_template.csv` in any text editor or Excel
2. **Replace** the example values with your real measurements
3. **Keep** the site names exactly as: SED1, SED2, SED3, SED4, SED5, SED6, SED7
4. **Add/remove** environmental variables as needed
5. **Save as** `environmental_data.csv`

### **Option 2: Use Excel**
1. **Open** `environmental_data_template.csv` in Excel
2. **Edit** with your real data
3. **Save as** `environmental_data.xlsx`

## 📊 **Template Format**

The template includes these example variables:
- **Site** (required): SED1, SED2, SED3, SED4, SED5, SED6, SED7
- **pH**: Acidity/alkalinity
- **Temperature**: Water/sediment temperature
- **Conductivity**: Electrical conductivity
- **Dissolved_Oxygen**: DO concentration
- **Nitrate**: Nitrogen content
- **Phosphate**: Phosphorus content  
- **Organic_Matter**: Organic matter percentage

## ✏️ **Customization**

### **Your Variables**
Replace the example columns with your actual measurements:
- Heavy metals (Pb, Cd, Zn, etc.)
- Nutrients (N, P, K, etc.)
- Physical parameters (grain size, moisture, etc.)
- Chemical parameters (TOC, COD, etc.)

### **Example with Your Data**
```csv
Site,pH,Lead_mg_kg,Zinc_mg_kg,Organic_Carbon_pct,Grain_Size_um
SED1,7.2,45.3,120.5,3.2,250
SED2,6.8,38.1,95.2,2.8,180
SED3,7.5,52.7,145.8,4.1,320
...
```

## 🚀 **Running the Analysis**

Once you have your data file ready:

1. **Make sure** your file is named either:
   - `environmental_data.csv` OR
   - `environmental_data.xlsx`

2. **Run the analysis**:
   ```r
   source("environmental_correlation_analysis.R")
   ```

3. **Results will be generated automatically**:
   - Environmental correlation plots
   - Statistical test results
   - Mantel test correlations
   - RDA variance partitioning

## 📈 **What You'll Get**

### **Figures**
- **`environmental_analysis_bacteria.png`** - 3-panel environmental analysis
- **`environmental_vectors_bacteria.png`** - PCoA with environmental vectors
- Similar figures for fungi and archaea

### **Statistical Results**
- **Environmental fitting**: Which variables correlate with community patterns
- **Mantel tests**: Overall environment-community correlation (r and p-value)
- **RDA results**: Variance explained by environmental variables

## ⚠️ **Important Notes**

1. **Site names must match exactly**: SED1, SED2, SED3, SED4, SED5, SED6, SED7
2. **One measurement per site** is perfect for this analysis
3. **Missing values**: Remove any columns with missing data
4. **Numeric data only**: Environmental variables must be numbers

## 🔍 **Troubleshooting**

### **If the script doesn't find your file:**
- Check the filename is exactly `environmental_data.csv` or `environmental_data.xlsx`
- Make sure it's in the same folder as the analysis scripts

### **If you get errors:**
- Check that site names match exactly (SED1, SED2, etc.)
- Ensure all environmental variables are numeric
- Remove any columns with missing values

## 🎯 **Ready to Go!**

Your analysis framework is complete and ready. Just:
1. ✏️ **Edit the template** with your real data
2. 💾 **Save** as `environmental_data.csv` or `.xlsx`
3. ▶️ **Run** the analysis script
4. 📊 **Interpret** the results using the comprehensive guide

**The template makes it super easy - just replace the example numbers with your measurements!**
