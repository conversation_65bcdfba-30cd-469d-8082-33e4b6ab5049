# Publication-Ready Analyses for Microbial Community Data
# Comprehensive analysis suite for "negative results" paper

library(readxl)
library(vegan)
library(ggplot2)
library(dplyr)
library(tidyr)
library(gridExtra)
library(RColorBrewer)
library(ape)
library(cluster)

# Function to prepare data for community analysis
prepare_community_data <- function(group_name) {
  # Read data
  otu_table <- read_excel(paste0("OTU_table_", group_name, ".xlsx"))
  metadata <- read_excel(paste0("metadata_", group_name, ".xlsx"))
  
  # Prepare matrices
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Add site number
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  
  # Transpose for vegan (samples as rows)
  community_matrix <- t(count_matrix)
  
  return(list(
    community = community_matrix,
    metadata = metadata,
    raw_counts = count_matrix
  ))
}

# 1. DIVERSITY ANALYSIS
analyze_diversity <- function(community_matrix, metadata, group_name) {
  cat("=== DIVERSITY ANALYSIS:", toupper(group_name), "===\n")
  
  # Calculate diversity indices
  diversity_data <- data.frame(
    Sample = rownames(community_matrix),
    Richness = specnumber(community_matrix),
    Shannon = diversity(community_matrix, index = "shannon"),
    Simpson = diversity(community_matrix, index = "simpson"),
    Evenness = diversity(community_matrix, index = "shannon") / log(specnumber(community_matrix))
  )
  
  # Merge with metadata
  diversity_data <- merge(diversity_data, metadata, by = "Sample")
  
  # Statistical tests
  cat("Alpha diversity comparisons (Wilcoxon tests):\n")
  for (index in c("Richness", "Shannon", "Simpson", "Evenness")) {
    biofilm_vals <- diversity_data[[index]][diversity_data$Substrate == "Biofilm"]
    sediment_vals <- diversity_data[[index]][diversity_data$Substrate == "Sediment"]
    
    test_result <- wilcox.test(biofilm_vals, sediment_vals)
    cat(index, "p-value:", round(test_result$p.value, 4), "\n")
  }
  
  # Create diversity plots
  diversity_long <- diversity_data %>%
    select(Sample, Substrate, SiteNumber, Richness, Shannon, Simpson, Evenness) %>%
    pivot_longer(cols = c(Richness, Shannon, Simpson, Evenness), 
                 names_to = "Index", values_to = "Value")
  
  p_diversity <- ggplot(diversity_long, aes(x = Substrate, y = Value, fill = Substrate)) +
    geom_boxplot(alpha = 0.7) +
    geom_jitter(width = 0.2, alpha = 0.6) +
    facet_wrap(~Index, scales = "free_y") +
    scale_fill_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    labs(title = paste("Alpha Diversity -", toupper(group_name)),
         x = "Substrate", y = "Diversity Value") +
    theme_minimal() +
    theme(legend.position = "none")
  
  return(list(data = diversity_data, plot = p_diversity))
}

# 2. ORDINATION ANALYSIS
analyze_ordination <- function(community_matrix, metadata, group_name) {
  cat("=== ORDINATION ANALYSIS:", toupper(group_name), "===\n")
  
  # Remove empty samples/species
  community_matrix <- community_matrix[rowSums(community_matrix) > 0, ]
  community_matrix <- community_matrix[, colSums(community_matrix) > 0]
  
  # Calculate distance matrix
  dist_matrix <- vegdist(community_matrix, method = "bray")
  
  # PCoA
  pcoa_result <- pcoa(dist_matrix)
  pcoa_data <- data.frame(
    Sample = rownames(community_matrix),
    PC1 = pcoa_result$vectors[, 1],
    PC2 = pcoa_result$vectors[, 2],
    PC3 = pcoa_result$vectors[, 3]
  )
  
  # Merge with metadata
  pcoa_data <- merge(pcoa_data, metadata, by = "Sample")
  
  # Calculate variance explained
  var_explained <- pcoa_result$values$Relative_eig[1:3] * 100
  
  # PERMANOVA tests
  cat("PERMANOVA results:\n")
  
  # Test substrate effect
  perm_substrate <- adonis2(dist_matrix ~ Substrate, data = metadata, permutations = 999)
  cat("Substrate effect - R2:", round(perm_substrate$R2[1], 4), 
      "p-value:", round(perm_substrate$`Pr(>F)`[1], 4), "\n")
  
  # Test site effect
  perm_site <- adonis2(dist_matrix ~ SiteNumber, data = metadata, permutations = 999)
  cat("Site effect - R2:", round(perm_site$R2[1], 4), 
      "p-value:", round(perm_site$`Pr(>F)`[1], 4), "\n")
  
  # Test nested effect (Site + Substrate)
  perm_nested <- adonis2(dist_matrix ~ SiteNumber + Substrate, data = metadata, permutations = 999)
  cat("Nested model - Site R2:", round(perm_nested$R2[1], 4), 
      "Substrate R2:", round(perm_nested$R2[2], 4), "\n")
  
  # PERMDISP (test for dispersion differences)
  disp_substrate <- betadisper(dist_matrix, metadata$Substrate)
  disp_test <- permutest(disp_substrate, permutations = 999)
  cat("PERMDISP (dispersion) p-value:", round(disp_test$tab$`Pr(>F)`[1], 4), "\n")
  
  # Create ordination plots
  p1 <- ggplot(pcoa_data, aes(x = PC1, y = PC2)) +
    geom_point(aes(color = Substrate, shape = SiteNumber), size = 3, alpha = 0.8) +
    scale_color_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    labs(title = paste("PCoA - Substrate Effect -", toupper(group_name)),
         x = paste0("PC1 (", round(var_explained[1], 1), "%)"),
         y = paste0("PC2 (", round(var_explained[2], 1), "%)")) +
    theme_minimal() +
    stat_ellipse(aes(color = Substrate), level = 0.68, linetype = "dashed")
  
  p2 <- ggplot(pcoa_data, aes(x = PC1, y = PC2)) +
    geom_point(aes(color = SiteNumber, shape = Substrate), size = 3, alpha = 0.8) +
    scale_color_brewer(type = "qual", palette = "Set1") +
    labs(title = paste("PCoA - Site Effect -", toupper(group_name)),
         x = paste0("PC1 (", round(var_explained[1], 1), "%)"),
         y = paste0("PC2 (", round(var_explained[2], 1), "%)")) +
    theme_minimal()
  
  return(list(
    pcoa_data = pcoa_data,
    var_explained = var_explained,
    permanova = list(substrate = perm_substrate, site = perm_site, nested = perm_nested),
    permdisp = disp_test,
    plots = list(substrate = p1, site = p2),
    distances = dist_matrix
  ))
}

# 3. BETA DIVERSITY ANALYSIS
analyze_beta_diversity <- function(dist_matrix, metadata, group_name) {
  cat("=== BETA DIVERSITY ANALYSIS:", toupper(group_name), "===\n")
  
  # Convert distance matrix to data frame
  dist_df <- data.frame(
    Sample1 = rep(rownames(as.matrix(dist_matrix)), each = nrow(as.matrix(dist_matrix))),
    Sample2 = rep(rownames(as.matrix(dist_matrix)), times = nrow(as.matrix(dist_matrix))),
    Distance = as.vector(as.matrix(dist_matrix))
  )
  
  # Remove self-comparisons
  dist_df <- dist_df[dist_df$Sample1 != dist_df$Sample2, ]
  
  # Add metadata for both samples
  dist_df <- merge(dist_df, metadata[, c("Sample", "Substrate", "SiteNumber")], 
                   by.x = "Sample1", by.y = "Sample")
  names(dist_df)[4:5] <- c("Substrate1", "Site1")
  
  dist_df <- merge(dist_df, metadata[, c("Sample", "Substrate", "SiteNumber")], 
                   by.x = "Sample2", by.y = "Sample")
  names(dist_df)[6:7] <- c("Substrate2", "Site2")
  
  # Classify comparisons
  dist_df$Comparison <- ifelse(
    dist_df$Substrate1 == dist_df$Substrate2,
    paste("Within", dist_df$Substrate1),
    "Between Substrates"
  )
  
  # Calculate summary statistics
  beta_summary <- dist_df %>%
    group_by(Comparison) %>%
    summarise(
      Mean = mean(Distance),
      Median = median(Distance),
      SD = sd(Distance),
      n = n(),
      .groups = "drop"
    )
  
  print(beta_summary)
  
  # Statistical test
  within_biofilm <- dist_df$Distance[dist_df$Comparison == "Within Biofilm"]
  within_sediment <- dist_df$Distance[dist_df$Comparison == "Within Sediment"]
  between_substrates <- dist_df$Distance[dist_df$Comparison == "Between Substrates"]
  
  # Test if between-substrate distances are larger than within-substrate
  if (length(within_biofilm) > 0 && length(between_substrates) > 0) {
    test1 <- wilcox.test(between_substrates, within_biofilm)
    cat("Between vs Within Biofilm p-value:", round(test1$p.value, 4), "\n")
  }
  
  if (length(within_sediment) > 0 && length(between_substrates) > 0) {
    test2 <- wilcox.test(between_substrates, within_sediment)
    cat("Between vs Within Sediment p-value:", round(test2$p.value, 4), "\n")
  }
  
  # Create beta diversity plot
  p_beta <- ggplot(dist_df, aes(x = Comparison, y = Distance, fill = Comparison)) +
    geom_boxplot(alpha = 0.7) +
    geom_jitter(width = 0.2, alpha = 0.4) +
    scale_fill_manual(values = c("Within Biofilm" = "lightblue", 
                                 "Within Sediment" = "lightcoral",
                                 "Between Substrates" = "gray")) +
    labs(title = paste("Beta Diversity Comparisons -", toupper(group_name)),
         x = "Comparison Type", y = "Bray-Curtis Distance") +
    theme_minimal() +
    theme(legend.position = "none", axis.text.x = element_text(angle = 45, hjust = 1))
  
  return(list(data = dist_df, summary = beta_summary, plot = p_beta))
}

# 4. DISPERSION ANALYSIS
analyze_dispersion <- function(dist_matrix, metadata, group_name) {
  cat("=== DISPERSION ANALYSIS:", toupper(group_name), "===\n")
  
  # Calculate dispersion
  disp_result <- betadisper(dist_matrix, metadata$Substrate)
  
  # Extract distances to centroid
  disp_data <- data.frame(
    Sample = names(disp_result$distances),
    Distance_to_Centroid = disp_result$distances,
    Substrate = metadata$Substrate[match(names(disp_result$distances), metadata$Sample)]
  )
  
  # Statistical test
  disp_test <- permutest(disp_result, permutations = 999)
  cat("Dispersion test p-value:", round(disp_test$tab$`Pr(>F)`[1], 4), "\n")
  
  # Summary statistics
  disp_summary <- disp_data %>%
    group_by(Substrate) %>%
    summarise(
      Mean_Dispersion = mean(Distance_to_Centroid),
      SD_Dispersion = sd(Distance_to_Centroid),
      n = n(),
      .groups = "drop"
    )
  
  print(disp_summary)
  
  # Create dispersion plot
  p_disp <- ggplot(disp_data, aes(x = Substrate, y = Distance_to_Centroid, fill = Substrate)) +
    geom_boxplot(alpha = 0.7) +
    geom_jitter(width = 0.2, alpha = 0.6) +
    scale_fill_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    labs(title = paste("Community Dispersion -", toupper(group_name)),
         x = "Substrate", y = "Distance to Centroid") +
    theme_minimal() +
    theme(legend.position = "none")
  
  return(list(data = disp_data, summary = disp_summary, plot = p_disp, test = disp_test))
}

# Main analysis function
comprehensive_analysis <- function(group_name) {
  cat("\n", paste(rep("=", 70), collapse = ""), "\n")
  cat("COMPREHENSIVE PUBLICATION ANALYSIS:", toupper(group_name), "\n")
  cat(paste(rep("=", 70), collapse = ""), "\n")
  
  # Prepare data
  data <- prepare_community_data(group_name)
  
  # Run all analyses
  diversity_results <- analyze_diversity(data$community, data$metadata, group_name)
  ordination_results <- analyze_ordination(data$community, data$metadata, group_name)
  beta_results <- analyze_beta_diversity(ordination_results$distances, data$metadata, group_name)
  dispersion_results <- analyze_dispersion(ordination_results$distances, data$metadata, group_name)
  
  # Create combined plots
  combined_plot <- grid.arrange(
    diversity_results$plot,
    ordination_results$plots$substrate,
    beta_results$plot,
    dispersion_results$plot,
    ncol = 2
  )
  
  # Save results
  ggsave(paste0("publication_figure_", group_name, ".png"), combined_plot, 
         width = 16, height = 12, dpi = 300)
  
  # Save ordination plots separately
  ggsave(paste0("pcoa_substrate_", group_name, ".png"), ordination_results$plots$substrate,
         width = 10, height = 8, dpi = 300)
  ggsave(paste0("pcoa_site_", group_name, ".png"), ordination_results$plots$site,
         width = 10, height = 8, dpi = 300)
  
  cat("\nFiles saved:\n")
  cat("- Combined figure:", paste0("publication_figure_", group_name, ".png"), "\n")
  cat("- PCoA substrate:", paste0("pcoa_substrate_", group_name, ".png"), "\n")
  cat("- PCoA site:", paste0("pcoa_site_", group_name, ".png"), "\n")
  
  return(list(
    diversity = diversity_results,
    ordination = ordination_results,
    beta_diversity = beta_results,
    dispersion = dispersion_results
  ))
}

# Run comprehensive analysis
cat("PUBLICATION-READY ANALYSES\n")
cat("==========================\n")

# Analyze bacteria (most comprehensive dataset)
bacteria_pub <- comprehensive_analysis("bacteria")

cat("\n=== ANALYSIS COMPLETE ===\n")
cat("Publication-ready figures and analyses generated.\n")
