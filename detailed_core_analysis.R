# Detailed Core Community Analysis with Lower Thresholds
# Focus on finding meaningful core communities at lower prevalence thresholds

library(readxl)
library(dplyr)
library(ggplot2)
library(vegan)
library(reshape2)
library(gridExtra)

# Function to analyze core communities at multiple thresholds
analyze_core_thresholds <- function(group_name, thresholds = c(0.2, 0.25, 0.3)) {
  cat("=== DETAILED ANALYSIS FOR", toupper(group_name), "===\n")
  
  # Read data
  otu_file <- paste0("OTU_table_", group_name, ".xlsx")
  metadata_file <- paste0("metadata_", group_name, ".xlsx")
  taxonomy_file <- paste0("taxonomy_", group_name, ".xlsx")
  
  otu_table <- read_excel(otu_file)
  metadata <- read_excel(metadata_file)
  taxonomy <- read_excel(taxonomy_file)
  
  # Prepare data
  otu_matrix <- as.matrix(otu_table[, -1])
  rownames(otu_matrix) <- otu_table[[1]]
  otu_matrix_t <- t(otu_matrix)
  
  results <- list()
  
  for (threshold in thresholds) {
    cat("\n--- ANALYSIS AT", threshold*100, "% PREVALENCE ---\n")
    
    # Overall analysis
    otu_rel <- otu_matrix_t / rowSums(otu_matrix_t, na.rm = TRUE)
    prevalence <- colSums(otu_rel > 0.001, na.rm = TRUE) / nrow(otu_rel)
    core_otus <- names(prevalence)[prevalence >= threshold]
    
    cat("Overall core OTUs:", length(core_otus), "\n")
    
    # Substrate-specific analysis
    substrates <- unique(metadata$Substrate)
    substrate_results <- list()
    
    for (substrate in substrates) {
      substrate_samples <- metadata[metadata$Substrate == substrate, "Sample"][[1]]
      substrate_cols <- which(colnames(otu_table) %in% substrate_samples)
      substrate_otu <- otu_table[, substrate_cols, drop = FALSE]
      substrate_otu_t <- t(substrate_otu[, -which(colnames(substrate_otu) == colnames(otu_table)[1])])
      
      # Calculate prevalence for this substrate
      if (nrow(substrate_otu_t) > 0) {
        substrate_rel <- substrate_otu_t / rowSums(substrate_otu_t, na.rm = TRUE)
        substrate_prev <- colSums(substrate_rel > 0.001, na.rm = TRUE) / nrow(substrate_rel)
        substrate_core <- names(substrate_prev)[substrate_prev >= threshold]
        
        substrate_results[[substrate]] <- list(
          core_otus = substrate_core,
          n_samples = nrow(substrate_otu_t),
          prevalence = substrate_prev
        )
        
        cat(substrate, "core OTUs:", length(substrate_core), "(", nrow(substrate_otu_t), "samples )\n")
      }
    }
    
    # Compare substrates
    if (length(substrate_results) == 2) {
      core1 <- substrate_results[[substrates[1]]]$core_otus
      core2 <- substrate_results[[substrates[2]]]$core_otus
      
      shared <- intersect(core1, core2)
      unique1 <- setdiff(core1, core2)
      unique2 <- setdiff(core2, core1)
      
      cat("Shared core OTUs:", length(shared), "\n")
      cat("Unique to", substrates[1], ":", length(unique1), "\n")
      cat("Unique to", substrates[2], ":", length(unique2), "\n")
      
      if (length(union(core1, core2)) > 0) {
        jaccard <- length(shared) / length(union(core1, core2))
        cat("Jaccard similarity:", round(jaccard, 3), "\n")
      }
      
      # Print some core OTU names if they exist
      if (length(shared) > 0) {
        cat("Shared core OTUs (first 10):", paste(head(shared, 10), collapse = ", "), "\n")
      }
      if (length(unique1) > 0) {
        cat(substrates[1], "specific (first 5):", paste(head(unique1, 5), collapse = ", "), "\n")
      }
      if (length(unique2) > 0) {
        cat(substrates[2], "specific (first 5):", paste(head(unique2, 5), collapse = ", "), "\n")
      }
    }
    
    results[[paste0("threshold_", threshold)]] <- list(
      overall_core = core_otus,
      substrate_results = substrate_results,
      threshold = threshold
    )
  }
  
  return(results)
}

# Function to get taxonomic information for core OTUs
get_core_taxonomy <- function(group_name, core_otus) {
  taxonomy_file <- paste0("taxonomy_", group_name, ".xlsx")
  taxonomy <- read_excel(taxonomy_file)
  
  # Match core OTUs with taxonomy
  core_tax <- taxonomy[taxonomy[[1]] %in% core_otus, ]
  return(core_tax)
}

# Function to create summary table
create_summary_table <- function(all_results) {
  summary_data <- data.frame()
  
  for (group in names(all_results)) {
    for (threshold_name in names(all_results[[group]])) {
      threshold_data <- all_results[[group]][[threshold_name]]
      threshold_val <- threshold_data$threshold
      
      # Overall
      n_overall <- length(threshold_data$overall_core)
      
      # Substrate-specific
      substrate_names <- names(threshold_data$substrate_results)
      if (length(substrate_names) == 2) {
        n_sub1 <- length(threshold_data$substrate_results[[substrate_names[1]]]$core_otus)
        n_sub2 <- length(threshold_data$substrate_results[[substrate_names[2]]]$core_otus)
        n_shared <- length(intersect(
          threshold_data$substrate_results[[substrate_names[1]]]$core_otus,
          threshold_data$substrate_results[[substrate_names[2]]]$core_otus
        ))
      } else {
        n_sub1 <- n_sub2 <- n_shared <- 0
      }
      
      row <- data.frame(
        Group = group,
        Threshold = threshold_val,
        Overall_Core = n_overall,
        Sediment_Core = n_sub1,
        Biofilm_Core = n_sub2,
        Shared_Core = n_shared
      )
      
      summary_data <- rbind(summary_data, row)
    }
  }
  
  return(summary_data)
}

# Run detailed analysis
cat("DETAILED CORE COMMUNITY ANALYSIS\n")
cat("=================================\n\n")

# Analyze all groups at multiple thresholds
all_results <- list()
all_results$bacteria <- analyze_core_thresholds("bacteria")
all_results$fungi <- analyze_core_thresholds("fungi") 
all_results$archaea <- analyze_core_thresholds("archaea")

# Create summary table
summary_table <- create_summary_table(all_results)
cat("\n=== SUMMARY TABLE ===\n")
print(summary_table)

# Save summary table
write.csv(summary_table, "core_community_summary_table.csv", row.names = FALSE)

# Get taxonomy for some core OTUs (at 20% threshold)
cat("\n=== TAXONOMIC INFORMATION FOR CORE OTUs (20% threshold) ===\n")

for (group in c("bacteria", "fungi", "archaea")) {
  core_otus_20 <- all_results[[group]]$threshold_0.2$overall_core
  if (length(core_otus_20) > 0) {
    cat("\n", toupper(group), "- Core OTUs at 20% prevalence:\n")
    core_tax <- get_core_taxonomy(group, core_otus_20)
    if (nrow(core_tax) > 0) {
      # Print first few with taxonomy
      for (i in 1:min(10, nrow(core_tax))) {
        cat("OTU:", core_tax[i, 1], "- Taxonomy:", paste(core_tax[i, 2:ncol(core_tax)], collapse = "; "), "\n")
      }
    }
  }
}

cat("\n=== ANALYSIS COMPLETE ===\n")
cat("Summary table saved to: core_community_summary_table.csv\n")
