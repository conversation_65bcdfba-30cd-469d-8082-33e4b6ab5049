# Simple Environmental Analysis
# Quick and reliable environmental correlation analysis

library(readxl)
library(vegan)
library(ggplot2)
library(dplyr)
library(ape)

# Function to run environmental analysis for one group
run_env_analysis <- function(group_name) {
  cat("=== ENVIRONMENTAL ANALYSIS:", toupper(group_name), "===\n")
  
  # Read microbial data
  otu_table <- read_excel(paste0("OTU_table_", group_name, ".xlsx"))
  metadata <- read_excel(paste0("metadata_", group_name, ".xlsx"))
  
  # Prepare community matrix
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Create site labels
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  metadata$SiteLabel <- paste0("SED", metadata$SiteNumber)
  
  # Filter to sediment samples only
  sediment_samples <- metadata$Sample[metadata$Substrate == "Sediment"]
  sediment_metadata <- metadata[metadata$Substrate == "Sediment", ]
  sediment_counts <- count_matrix[, sediment_samples]
  
  cat("Sediment samples:", ncol(sediment_counts), "\n")
  cat("Sediment sites:", length(unique(sediment_metadata$SiteLabel)), "\n")
  
  # Convert to relative abundance
  sediment_rel <- sweep(sediment_counts, 2, colSums(sediment_counts), "/")
  
  # Aggregate to site level
  site_communities <- list()
  for (site in unique(sediment_metadata$SiteLabel)) {
    site_samples <- sediment_metadata$Sample[sediment_metadata$SiteLabel == site]
    site_indices <- match(site_samples, colnames(sediment_rel))
    
    if (length(site_indices) > 1) {
      site_community <- rowMeans(sediment_rel[, site_indices, drop = FALSE])
    } else {
      site_community <- sediment_rel[, site_indices]
    }
    site_communities[[site]] <- site_community
  }
  
  # Convert to matrix
  site_matrix <- do.call(cbind, site_communities)
  colnames(site_matrix) <- names(site_communities)
  site_matrix <- site_matrix[rowSums(site_matrix) > 0, ]
  
  cat("Site-aggregated matrix:", nrow(site_matrix), "taxa x", ncol(site_matrix), "sites\n")
  
  # Read environmental data
  env_data <- read.csv("environmental_data_clean.csv", stringsAsFactors = FALSE)
  cat("Environmental data:", nrow(env_data), "sites,", ncol(env_data)-1, "variables\n")
  
  # Match sites
  common_sites <- intersect(colnames(site_matrix), env_data$Site)
  site_matrix <- site_matrix[, common_sites]
  env_data <- env_data[env_data$Site %in% common_sites, ]
  env_data <- env_data[match(common_sites, env_data$Site), ]
  
  cat("Common sites:", length(common_sites), "\n")
  print(common_sites)
  
  # Prepare environmental matrix
  env_matrix <- env_data[, !colnames(env_data) %in% c("Site"), drop = FALSE]
  rownames(env_matrix) <- env_data$Site
  
  # Remove columns with missing values
  complete_cols <- sapply(env_matrix, function(x) !any(is.na(x)))
  env_matrix <- env_matrix[, complete_cols, drop = FALSE]
  
  cat("Environmental variables:", ncol(env_matrix), "\n")
  print(colnames(env_matrix))
  
  # Community analysis
  community_t <- t(site_matrix)
  comm_dist <- vegdist(community_t, method = "bray")
  
  # PCoA
  pcoa_result <- pcoa(comm_dist)
  pcoa_scores <- pcoa_result$vectors[, 1:2]
  var_explained <- pcoa_result$values$Relative_eig[1:2] * 100
  
  # Environmental fitting
  env_fit <- envfit(pcoa_scores, env_matrix, permutations = 999)
  cat("\nEnvironmental fitting results:\n")
  print(env_fit)
  
  # Mantel test
  env_dist <- dist(scale(env_matrix))
  mantel_result <- mantel(comm_dist, env_dist, permutations = 999)
  cat("\nMantel test (community ~ environment):\n")
  cat("Correlation:", round(mantel_result$statistic, 4), "\n")
  cat("p-value:", round(mantel_result$signif, 4), "\n")
  
  # Create plot
  plot_data <- data.frame(
    Site = rownames(pcoa_scores),
    PC1 = pcoa_scores[, 1],
    PC2 = pcoa_scores[, 2]
  )
  
  # Add environmental data
  plot_data <- cbind(plot_data, env_matrix[rownames(pcoa_scores), ])
  
  # PCoA plot with environmental vectors
  p1 <- ggplot(plot_data, aes(x = PC1, y = PC2)) +
    geom_point(size = 4, alpha = 0.8, color = "darkred") +
    geom_text(aes(label = Site), vjust = -1.5, size = 3, fontface = "bold") +
    labs(
      title = paste("Environmental Correlations -", toupper(group_name)),
      subtitle = paste("Mantel r =", round(mantel_result$statistic, 3), 
                      ", p =", round(mantel_result$signif, 3)),
      x = paste0("PC1 (", round(var_explained[1], 1), "%)"),
      y = paste0("PC2 (", round(var_explained[2], 1), "%)")
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12)
    )
  
  # Add significant environmental vectors
  if (!is.null(env_fit$vectors)) {
    sig_vectors <- env_fit$vectors$pvals <= 0.1
    if (any(sig_vectors)) {
      vector_coords <- env_fit$vectors$arrows[sig_vectors, , drop = FALSE]
      vector_data <- data.frame(
        Variable = rownames(vector_coords),
        PC1_end = vector_coords[, 1] * 0.8,
        PC2_end = vector_coords[, 2] * 0.8,
        pvalue = env_fit$vectors$pvals[sig_vectors]
      )
      
      p1 <- p1 +
        geom_segment(data = vector_data,
                     aes(x = 0, y = 0, xend = PC1_end, yend = PC2_end),
                     arrow = arrow(length = unit(0.3, "cm")),
                     color = "blue", linewidth = 1) +
        geom_text(data = vector_data,
                  aes(x = PC1_end * 1.1, y = PC2_end * 1.1, label = Variable),
                  color = "blue", fontface = "bold", size = 3)
    }
  }
  
  # Environmental gradient plot (using pH as example)
  p2 <- ggplot(plot_data, aes(x = PC1, y = PC2, color = pH)) +
    geom_point(size = 4, alpha = 0.8) +
    geom_text(aes(label = Site), vjust = -1.5, size = 3, fontface = "bold") +
    scale_color_gradient(low = "lightblue", high = "darkblue") +
    labs(
      title = paste("pH Gradient -", toupper(group_name)),
      x = paste0("PC1 (", round(var_explained[1], 1), "%)"),
      y = paste0("PC2 (", round(var_explained[2], 1), "%)")
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(size = 14, face = "bold")
    )
  
  # Save plots
  ggsave(paste0("environmental_vectors_", group_name, ".png"), p1,
         width = 10, height = 8, dpi = 300)
  ggsave(paste0("environmental_gradient_", group_name, ".png"), p2,
         width = 10, height = 8, dpi = 300)
  
  cat("\nFiles saved:\n")
  cat("- environmental_vectors_", group_name, ".png\n")
  cat("- environmental_gradient_", group_name, ".png\n\n")
  
  return(list(
    mantel = mantel_result,
    env_fit = env_fit,
    plots = list(vectors = p1, gradient = p2)
  ))
}

# Main execution
cat("SIMPLE ENVIRONMENTAL CORRELATION ANALYSIS\n")
cat("=========================================\n\n")

# Run analysis for all groups
bacteria_env <- run_env_analysis("bacteria")
fungi_env <- run_env_analysis("fungi")
archaea_env <- run_env_analysis("archaea")

# Create summary
summary_results <- data.frame(
  Group = c("Bacteria", "Fungi", "Archaea"),
  Mantel_r = c(
    round(bacteria_env$mantel$statistic, 4),
    round(fungi_env$mantel$statistic, 4),
    round(archaea_env$mantel$statistic, 4)
  ),
  Mantel_p = c(
    round(bacteria_env$mantel$signif, 4),
    round(fungi_env$mantel$signif, 4),
    round(archaea_env$mantel$signif, 4)
  ),
  Significant = c(
    bacteria_env$mantel$signif < 0.05,
    fungi_env$mantel$signif < 0.05,
    archaea_env$mantel$signif < 0.05
  )
)

write.csv(summary_results, "environmental_correlation_summary.csv", row.names = FALSE)

cat("=== ENVIRONMENTAL ANALYSIS SUMMARY ===\n")
print(summary_results)

cat("\n=== INTERPRETATION ===\n")
for (i in 1:nrow(summary_results)) {
  group <- summary_results$Group[i]
  r <- summary_results$Mantel_r[i]
  p <- summary_results$Mantel_p[i]
  
  if (p < 0.05) {
    strength <- ifelse(abs(r) > 0.5, "strong", ifelse(abs(r) > 0.3, "moderate", "weak"))
    cat(group, ": SIGNIFICANT", strength, "correlation (r =", r, ", p =", p, ")\n")
  } else {
    cat(group, ": No significant correlation (r =", r, ", p =", p, ")\n")
  }
}

cat("\n=== FILES GENERATED ===\n")
cat("Environmental vector plots: environmental_vectors_*.png\n")
cat("Environmental gradient plots: environmental_gradient_*.png\n")
cat("Summary table: environmental_correlation_summary.csv\n")

cat("\n🎯 ENVIRONMENTAL ANALYSIS COMPLETE! 🎯\n")
