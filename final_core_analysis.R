# Final Core Community Analysis - Corrected Version
# Analysis of core communities with proper data handling

library(readxl)
library(dplyr)
library(ggplot2)

# Function to analyze core communities with corrected substrate handling
analyze_core_final <- function(group_name) {
  cat("=== ANALYZING", toupper(group_name), "===\n")
  
  # Read data
  otu_file <- paste0("OTU_table_", group_name, ".xlsx")
  metadata_file <- paste0("metadata_", group_name, ".xlsx")
  taxonomy_file <- paste0("taxonomy_", group_name, ".xlsx")
  
  otu_table <- read_excel(otu_file)
  metadata <- read_excel(metadata_file)
  taxonomy <- read_excel(taxonomy_file)
  
  cat("Data dimensions:\n")
  cat("- OTU table:", dim(otu_table), "\n")
  cat("- Metadata:", dim(metadata), "\n")
  cat("- Taxonomy:", dim(taxonomy), "\n")
  
  # Prepare OTU matrix (samples as rows, OTUs as columns)
  otu_matrix <- as.matrix(otu_table[, -1])
  rownames(otu_matrix) <- otu_table[[1]]
  otu_matrix_t <- t(otu_matrix)
  
  # Overall core community analysis at different thresholds
  cat("\n--- OVERALL CORE COMMUNITY ---\n")
  thresholds <- c(0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2)
  
  overall_results <- list()
  for (thresh in thresholds) {
    # Convert to relative abundance
    otu_rel <- otu_matrix_t / rowSums(otu_matrix_t, na.rm = TRUE)
    
    # Calculate prevalence
    prevalence <- colSums(otu_rel > 0.001, na.rm = TRUE) / nrow(otu_rel)
    
    # Identify core OTUs
    core_otus <- names(prevalence)[prevalence >= thresh]
    
    overall_results[[paste0("thresh_", thresh)]] <- list(
      threshold = thresh,
      n_core = length(core_otus),
      core_otus = core_otus,
      prevalence = prevalence
    )
    
    cat("At", thresh*100, "% prevalence:", length(core_otus), "core OTUs\n")
  }
  
  # Substrate-specific analysis
  cat("\n--- SUBSTRATE-SPECIFIC ANALYSIS ---\n")
  
  # Get substrate information
  substrate_counts <- table(metadata$Substrate)
  cat("Substrate distribution:\n")
  print(substrate_counts)
  
  substrate_results <- list()
  
  for (substrate in names(substrate_counts)) {
    cat("\n--- Substrate:", substrate, "---\n")
    
    # Get samples for this substrate
    substrate_samples <- metadata$Sample[metadata$Substrate == substrate]
    cat("Samples in", substrate, ":", length(substrate_samples), "\n")
    
    # Find matching columns in OTU table
    matching_cols <- which(colnames(otu_table) %in% substrate_samples)
    cat("Matching columns found:", length(matching_cols), "\n")
    
    if (length(matching_cols) > 0) {
      # Extract substrate-specific OTU data
      substrate_otu <- otu_table[, matching_cols, drop = FALSE]
      substrate_matrix <- as.matrix(substrate_otu)
      rownames(substrate_matrix) <- otu_table[[1]]
      substrate_matrix_t <- t(substrate_matrix)
      
      cat("Substrate matrix dimensions:", dim(substrate_matrix_t), "\n")
      
      # Analyze at 20% threshold
      if (nrow(substrate_matrix_t) > 0) {
        substrate_rel <- substrate_matrix_t / rowSums(substrate_matrix_t, na.rm = TRUE)
        substrate_prev <- colSums(substrate_rel > 0.001, na.rm = TRUE) / nrow(substrate_rel)
        substrate_core_20 <- names(substrate_prev)[substrate_prev >= 0.2]
        
        substrate_results[[substrate]] <- list(
          n_samples = nrow(substrate_matrix_t),
          core_otus_20 = substrate_core_20,
          prevalence = substrate_prev
        )
        
        cat("Core OTUs at 20% prevalence:", length(substrate_core_20), "\n")
      }
    }
  }
  
  # Compare substrates if we have results for both
  if (length(substrate_results) == 2) {
    substrates <- names(substrate_results)
    core1 <- substrate_results[[substrates[1]]]$core_otus_20
    core2 <- substrate_results[[substrates[2]]]$core_otus_20
    
    cat("\n--- SUBSTRATE COMPARISON (20% threshold) ---\n")
    cat("Core OTUs in", substrates[1], ":", length(core1), "\n")
    cat("Core OTUs in", substrates[2], ":", length(core2), "\n")
    
    if (length(core1) > 0 && length(core2) > 0) {
      shared <- intersect(core1, core2)
      unique1 <- setdiff(core1, core2)
      unique2 <- setdiff(core2, core1)
      
      cat("Shared core OTUs:", length(shared), "\n")
      cat("Unique to", substrates[1], ":", length(unique1), "\n")
      cat("Unique to", substrates[2], ":", length(unique2), "\n")
      
      # Jaccard similarity
      jaccard <- length(shared) / length(union(core1, core2))
      cat("Jaccard similarity:", round(jaccard, 3), "\n")
      
      # Print some examples
      if (length(shared) > 0) {
        cat("Examples of shared core OTUs:", paste(head(shared, 5), collapse = ", "), "\n")
      }
      if (length(unique1) > 0) {
        cat("Examples unique to", substrates[1], ":", paste(head(unique1, 3), collapse = ", "), "\n")
      }
      if (length(unique2) > 0) {
        cat("Examples unique to", substrates[2], ":", paste(head(unique2, 3), collapse = ", "), "\n")
      }
    } else {
      cat("No core OTUs found for comparison\n")
    }
  }
  
  # Get taxonomy for core OTUs at 20% threshold
  core_20_overall <- overall_results$thresh_0.2$core_otus
  if (length(core_20_overall) > 0) {
    cat("\n--- TAXONOMY OF CORE OTUs (20% overall prevalence) ---\n")
    core_taxonomy <- taxonomy[taxonomy[[1]] %in% core_20_overall, ]
    
    if (nrow(core_taxonomy) > 0) {
      cat("First 10 core OTUs with taxonomy:\n")
      for (i in 1:min(10, nrow(core_taxonomy))) {
        otu_id <- core_taxonomy[i, 1]
        tax_string <- paste(core_taxonomy[i, 2:ncol(core_taxonomy)], collapse = "; ")
        cat("OTU", otu_id, ":", tax_string, "\n")
      }
    }
  }
  
  return(list(
    overall = overall_results,
    substrate = substrate_results,
    taxonomy = taxonomy
  ))
}

# Run analysis for all groups
cat("COMPREHENSIVE CORE COMMUNITY ANALYSIS\n")
cat("=====================================\n\n")

results <- list()
results$bacteria <- analyze_core_final("bacteria")
results$fungi <- analyze_core_final("fungi")
results$archaea <- analyze_core_final("archaea")

# Create summary table
cat("\n=== SUMMARY TABLE ===\n")
summary_df <- data.frame(
  Group = character(),
  Threshold = numeric(),
  Overall_Core = numeric(),
  Sediment_Core = numeric(),
  Biofilm_Core = numeric(),
  Shared_Core = numeric(),
  stringsAsFactors = FALSE
)

for (group in names(results)) {
  for (thresh_name in names(results[[group]]$overall)) {
    thresh_val <- results[[group]]$overall[[thresh_name]]$threshold
    overall_core <- results[[group]]$overall[[thresh_name]]$n_core
    
    # Get substrate-specific numbers
    substrate_res <- results[[group]]$substrate
    if (length(substrate_res) == 2) {
      substrates <- names(substrate_res)
      sed_core <- length(substrate_res[[substrates[1]]]$core_otus_20)
      bio_core <- length(substrate_res[[substrates[2]]]$core_otus_20)
      shared_core <- length(intersect(
        substrate_res[[substrates[1]]]$core_otus_20,
        substrate_res[[substrates[2]]]$core_otus_20
      ))
    } else {
      sed_core <- bio_core <- shared_core <- 0
    }
    
    summary_df <- rbind(summary_df, data.frame(
      Group = group,
      Threshold = thresh_val,
      Overall_Core = overall_core,
      Sediment_Core = sed_core,
      Biofilm_Core = bio_core,
      Shared_Core = shared_core
    ))
  }
}

print(summary_df)

# Save results
write.csv(summary_df, "final_core_community_summary.csv", row.names = FALSE)

cat("\n=== ANALYSIS COMPLETE ===\n")
cat("Summary saved to: final_core_community_summary.csv\n")
