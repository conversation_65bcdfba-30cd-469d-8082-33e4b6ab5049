# Create Corrected Combined Figure for All Groups
# Combine the corrected individual figures into publication-ready layouts

library(ggplot2)
library(gridExtra)
library(png)
library(grid)
library(dplyr)

# Function to create corrected combined ordination plot
create_corrected_ordination_comparison <- function() {
  
  # Read the corrected PCoA substrate plots
  bacteria_plot <- readPNG("corrected_pcoa_substrate_bacteria.png")
  fungi_plot <- readPNG("corrected_pcoa_substrate_fungi.png")
  archaea_plot <- readPNG("corrected_pcoa_substrate_archaea.png")
  
  # Create grid arrangement
  grid.arrange(
    rasterGrob(bacteria_plot),
    rasterGrob(fungi_plot), 
    rasterGrob(archaea_plot),
    ncol = 3,
    top = textGrob("PCoA Ordination - Substrate Effects (Corrected Site Labels)", 
                   gp = gpar(fontsize = 16, fontface = "bold"))
  )
}

# Function to create summary of corrected results
create_corrected_summary_plot <- function() {
  
  # Read the corrected summary table
  summary_data <- read.csv("corrected_summary_table.csv")
  
  # Create a summary plot showing the key differences
  summary_long <- summary_data %>%
    select(Group, Substrate_R2, Substrate_pvalue) %>%
    mutate(
      Significant = ifelse(Substrate_pvalue < 0.05, "Significant", "Not Significant"),
      Significance_Level = case_when(
        Substrate_pvalue < 0.001 ~ "p < 0.001",
        Substrate_pvalue < 0.01 ~ "p < 0.01", 
        Substrate_pvalue < 0.05 ~ "p < 0.05",
        Substrate_pvalue < 0.1 ~ "p < 0.1",
        TRUE ~ "p ≥ 0.1"
      )
    )
  
  # Create bar plot of R² values
  p1 <- ggplot(summary_long, aes(x = Group, y = Substrate_R2 * 100, fill = Significant)) +
    geom_col(alpha = 0.8) +
    geom_text(aes(label = paste0(round(Substrate_R2 * 100, 2), "%\n", Significance_Level)), 
              vjust = -0.5, size = 3) +
    scale_fill_manual(values = c("Significant" = "darkgreen", "Not Significant" = "gray60")) +
    labs(
      title = "Substrate Effects Across Microbial Groups (Corrected Analysis)",
      subtitle = "Independent biofilm sites (BF1, BF2) vs sediment sites (SED1-SED7)",
      x = "Microbial Group",
      y = "Variance Explained by Substrate (%)",
      fill = "Statistical Significance"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12),
      legend.position = "bottom"
    ) +
    ylim(0, max(summary_long$Substrate_R2 * 100) * 1.3)
  
  return(p1)
}

# Function to create study design visualization
create_study_design_plot <- function() {
  
  # Create a simple study design diagram
  design_data <- data.frame(
    Site = c("BF1", "BF2", "SED1", "SED2", "SED3", "SED4", "SED5", "SED6", "SED7"),
    Substrate = c("Biofilm", "Biofilm", rep("Sediment", 7)),
    Samples = c(6, 6, 6, 6, 6, 6, 6, 6, 6),
    x = c(1, 2, 4, 5, 6, 7, 8, 9, 10),
    y = c(2, 2, 1, 1, 1, 1, 1, 1, 1)
  )
  
  p_design <- ggplot(design_data, aes(x = x, y = y)) +
    geom_point(aes(color = Substrate, size = Samples), alpha = 0.8) +
    geom_text(aes(label = Site), vjust = -1.5, size = 3, fontface = "bold") +
    geom_text(aes(label = paste(Samples, "samples")), vjust = 2, size = 2.5) +
    scale_color_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    scale_size_continuous(range = c(8, 12), guide = "none") +
    labs(
      title = "Study Design - Independent Site Structure",
      subtitle = "Biofilm and sediment samples come from completely different locations",
      x = "",
      y = "",
      color = "Substrate Type"
    ) +
    theme_minimal() +
    theme(
      axis.text = element_blank(),
      axis.ticks = element_blank(),
      panel.grid = element_blank(),
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12),
      legend.position = "bottom"
    ) +
    annotate("text", x = 1.5, y = 2.5, label = "Biofilm Sites\n(Independent locations)", 
             size = 3, fontface = "italic", color = "darkblue") +
    annotate("text", x = 7, y = 0.5, label = "Sediment Sites\n(Independent locations)", 
             size = 3, fontface = "italic", color = "darkred")
  
  return(p_design)
}

# Main execution
cat("CREATING CORRECTED COMBINED FIGURES\n")
cat("===================================\n")

# Create summary plot
summary_plot <- create_corrected_summary_plot()
ggsave("corrected_substrate_effects_summary.png", summary_plot, 
       width = 10, height = 6, dpi = 300)

# Create study design plot
design_plot <- create_study_design_plot()
ggsave("study_design_corrected.png", design_plot, 
       width = 12, height = 6, dpi = 300)

# Create combined summary figure
combined_summary <- grid.arrange(
  summary_plot,
  design_plot,
  ncol = 1
)

ggsave("corrected_publication_summary.png", combined_summary, 
       width = 12, height = 10, dpi = 300)

cat("\nCorrected figures created:\n")
cat("1. corrected_substrate_effects_summary.png - Bar chart of substrate effects\n")
cat("2. study_design_corrected.png - Study design visualization\n") 
cat("3. corrected_publication_summary.png - Combined summary figure\n")
cat("4. Individual corrected figures for each group already created\n")

cat("\n=== KEY FINDINGS WITH CORRECTED ANALYSIS ===\n")
cat("Bacteria: R² = 3.26%, p = 0.037 (SIGNIFICANT)\n")
cat("Fungi: R² = 2.89%, p = 0.112 (NOT SIGNIFICANT)\n") 
cat("Archaea: R² = 2.07%, p = 0.302 (NOT SIGNIFICANT)\n")
cat("\nStudy design: 2 independent biofilm sites, 7 independent sediment sites\n")
cat("Site effects within sediment: Strong (28-41% variance explained)\n")
