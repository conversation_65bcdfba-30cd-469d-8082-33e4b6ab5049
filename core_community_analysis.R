# Core Community Analysis for Fungi, Bacteria, and Archaea
# Analysis of core community across samples and substrate types

# Load required libraries
library(readxl)
library(dplyr)
library(ggplot2)
library(vegan)
library(reshape2)
library(gridExtra)

# Function to read and process OTU data
read_otu_data <- function(otu_file, metadata_file, taxonomy_file) {
  # Read data
  otu_table <- read_excel(otu_file)
  metadata <- read_excel(metadata_file)
  taxonomy <- read_excel(taxonomy_file)

  # Display data structure
  cat("OTU table dimensions:", dim(otu_table), "\n")
  cat("Metadata dimensions:", dim(metadata), "\n")
  cat("Taxonomy dimensions:", dim(taxonomy), "\n")
  cat("Metadata columns:", colnames(metadata), "\n")
  cat("Sample columns in OTU table:", colnames(otu_table)[2:6], "...\n")

  return(list(otu = otu_table, metadata = metadata, taxonomy = taxonomy))
}

# Function to identify core community
identify_core_community <- function(otu_table, prevalence_threshold = 0.5, abundance_threshold = 0.001) {
  # Convert to relative abundance if needed
  if (max(otu_table, na.rm = TRUE) > 1) {
    otu_rel <- otu_table / rowSums(otu_table, na.rm = TRUE)
  } else {
    otu_rel <- otu_table
  }
  
  # Calculate prevalence (proportion of samples where OTU is present)
  prevalence <- colSums(otu_rel > abundance_threshold, na.rm = TRUE) / nrow(otu_rel)
  
  # Identify core OTUs
  core_otus <- names(prevalence)[prevalence >= prevalence_threshold]
  
  cat("Total OTUs:", ncol(otu_table), "\n")
  cat("Core OTUs (prevalence >=", prevalence_threshold, "):", length(core_otus), "\n")
  cat("Percentage of core OTUs:", round(length(core_otus)/ncol(otu_table)*100, 2), "%\n")
  
  return(list(
    core_otus = core_otus,
    prevalence = prevalence,
    core_table = otu_rel[, core_otus, drop = FALSE]
  ))
}

# Function to compare core communities between substrate types
compare_substrate_cores <- function(otu_table, metadata, substrate_col = "Substrate") {
  # Get unique substrate types
  substrates <- unique(metadata[[substrate_col]])
  cat("Substrate types found:", paste(substrates, collapse = ", "), "\n")

  core_results <- list()

  for (substrate in substrates) {
    # Get samples for this substrate
    substrate_samples <- metadata[metadata[[substrate_col]] == substrate, "Sample"][[1]]

    # Filter OTU table for these samples (samples are columns, so transpose)
    substrate_cols <- which(colnames(otu_table) %in% substrate_samples)
    substrate_otu <- otu_table[, substrate_cols, drop = FALSE]

    # Transpose so samples are rows and OTUs are columns
    substrate_otu_t <- t(substrate_otu)

    cat("\n--- Substrate:", substrate, "---\n")
    cat("Number of samples:", nrow(substrate_otu_t), "\n")
    cat("Number of OTUs:", ncol(substrate_otu_t), "\n")

    # Identify core community for this substrate (use lower threshold for substrate-specific)
    core_results[[substrate]] <- identify_core_community(substrate_otu_t, prevalence_threshold = 0.3)
  }

  return(core_results)
}

# Function to create visualization
plot_core_analysis <- function(core_results, microbial_group) {
  # Extract prevalence data for plotting
  prevalence_data <- data.frame()

  for (substrate in names(core_results)) {
    if (length(core_results[[substrate]]$prevalence) > 0 && length(core_results[[substrate]]$core_otus) > 0) {
      prev_df <- data.frame(
        OTU = names(core_results[[substrate]]$prevalence),
        Prevalence = core_results[[substrate]]$prevalence,
        Substrate = substrate,
        stringsAsFactors = FALSE
      )
      prevalence_data <- rbind(prevalence_data, prev_df)
    }
  }

  # Create prevalence distribution plot
  if (nrow(prevalence_data) > 0) {
    p1 <- ggplot(prevalence_data, aes(x = Prevalence, fill = Substrate)) +
      geom_histogram(alpha = 0.7, bins = 30, position = "identity") +
      geom_vline(xintercept = 0.5, linetype = "dashed", color = "red") +
      labs(title = paste("OTU Prevalence Distribution -", microbial_group),
           x = "Prevalence", y = "Number of OTUs") +
      theme_minimal() +
      facet_wrap(~Substrate, ncol = 1)
  } else {
    p1 <- ggplot() +
      labs(title = paste("No data available for", microbial_group)) +
      theme_minimal()
  }

  return(p1)
}

# Main analysis function
analyze_microbial_group <- function(group_name) {
  cat("=== ANALYZING", toupper(group_name), "===\n")
  
  # File paths
  otu_file <- paste0("OTU_table_", group_name, ".xlsx")
  metadata_file <- paste0("metadata_", group_name, ".xlsx")
  taxonomy_file <- paste0("taxonomy_", group_name, ".xlsx")
  
  # Read data
  data <- read_otu_data(otu_file, metadata_file, taxonomy_file)

  # Prepare OTU table (OTUs are rows, samples are columns)
  # Transpose so samples are rows and OTUs are columns for analysis
  otu_matrix <- as.matrix(data$otu[, -1])  # Remove first column (OTU names)
  rownames(otu_matrix) <- data$otu[[1]]    # Set OTU names as row names
  otu_matrix_t <- t(otu_matrix)            # Transpose: samples as rows, OTUs as columns

  # Overall core community analysis
  cat("\n--- OVERALL CORE COMMUNITY ---\n")

  # Test different prevalence thresholds
  thresholds <- c(0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2)
  for (thresh in thresholds) {
    core_test <- identify_core_community(otu_matrix_t, prevalence_threshold = thresh)
    cat("At", thresh*100, "% prevalence:", length(core_test$core_otus), "core OTUs\n")
  }

  overall_core <- identify_core_community(otu_matrix_t)
  
  # Substrate-specific core community analysis
  cat("\n--- SUBSTRATE-SPECIFIC CORE COMMUNITIES ---\n")
  substrate_cores <- compare_substrate_cores(data$otu, data$metadata)
  
  # Compare core communities between substrates
  if (length(substrate_cores) == 2) {
    substrates <- names(substrate_cores)
    core1 <- substrate_cores[[substrates[1]]]$core_otus
    core2 <- substrate_cores[[substrates[2]]]$core_otus

    cat("\n--- CORE COMMUNITY COMPARISON ---\n")
    cat("Core OTUs in", substrates[1], ":", length(core1), "\n")
    cat("Core OTUs in", substrates[2], ":", length(core2), "\n")
    cat("Shared core OTUs:", length(intersect(core1, core2)), "\n")
    cat("Unique to", substrates[1], ":", length(setdiff(core1, core2)), "\n")
    cat("Unique to", substrates[2], ":", length(setdiff(core2, core1)), "\n")

    # Jaccard similarity
    if (length(union(core1, core2)) > 0) {
      jaccard <- length(intersect(core1, core2)) / length(union(core1, core2))
      cat("Jaccard similarity:", round(jaccard, 3), "\n")
    } else {
      cat("Jaccard similarity: No core OTUs found\n")
    }

    # Print core OTU names if any exist
    if (length(core1) > 0) {
      cat("\nCore OTUs in", substrates[1], ":\n")
      cat(paste(core1, collapse = ", "), "\n")
    }
    if (length(core2) > 0) {
      cat("\nCore OTUs in", substrates[2], ":\n")
      cat(paste(core2, collapse = ", "), "\n")
    }
    if (length(intersect(core1, core2)) > 0) {
      cat("\nShared core OTUs:\n")
      cat(paste(intersect(core1, core2), collapse = ", "), "\n")
    }
  }
  
  # Create visualization
  plot <- plot_core_analysis(substrate_cores, group_name)
  
  return(list(
    overall_core = overall_core,
    substrate_cores = substrate_cores,
    plot = plot,
    data = data
  ))
}

# Run analysis for all three microbial groups
cat("Starting Core Community Analysis\n")
cat("================================\n\n")

# Analyze each group
results <- list()
results$bacteria <- analyze_microbial_group("bacteria")
results$fungi <- analyze_microbial_group("fungi")
results$archaea <- analyze_microbial_group("archaea")

# Create combined visualization
combined_plots <- grid.arrange(
  results$bacteria$plot,
  results$fungi$plot,
  results$archaea$plot,
  ncol = 3
)

# Save results
ggsave("core_community_analysis.png", combined_plots, width = 15, height = 8, dpi = 300)

cat("\n=== ANALYSIS COMPLETE ===\n")
cat("Results saved to core_community_analysis.png\n")
