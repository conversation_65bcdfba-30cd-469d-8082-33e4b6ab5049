# Verify Bacterial Families Fix
# Check what happened with Incertae sedis

library(readxl)
library(dplyr)

# Read bacteria data
otu_table <- read_excel("OTU_table_bacteria.xlsx")
metadata <- read_excel("metadata_bacteria.xlsx")
taxonomy <- read_excel("taxonomy_bacteria.xlsx")

# Check for Incertae sedis in the taxonomy
family_col <- taxonomy$Family
incertae_patterns <- c("Incertae sedis", "incertae sedis", "Incertae_sedis", "incertae_sedis")

# Find Incertae sedis entries
incertae_indices <- which(family_col %in% incertae_patterns)
incertae_families <- unique(family_col[incertae_indices])

cat("BACTERIAL FAMILIES INCERTAE SEDIS VERIFICATION\n")
cat("==============================================\n\n")

if (length(incertae_indices) > 0) {
  cat("✓ Found Incertae sedis in bacterial families:\n")
  cat("- Number of OTUs with Incertae sedis:", length(incertae_indices), "\n")
  cat("- Incertae sedis variants found:", paste(incertae_families, collapse = ", "), "\n")
  
  # Calculate abundance
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples and taxonomy
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  common_otus <- intersect(rownames(count_matrix), taxonomy[[1]])
  
  count_matrix <- count_matrix[common_otus, common_samples]
  taxonomy_matched <- taxonomy[taxonomy[[1]] %in% common_otus, ]
  
  # Calculate relative abundance
  rel_abundance <- sweep(count_matrix, 2, colSums(count_matrix), "/") * 100
  
  # Get Incertae sedis OTUs
  incertae_otus <- taxonomy_matched[[1]][taxonomy_matched$Family %in% incertae_patterns]
  incertae_abundance <- rel_abundance[incertae_otus, , drop = FALSE]
  
  # Calculate total Incertae sedis abundance per sample
  incertae_totals <- colSums(incertae_abundance)
  
  cat("- Mean Incertae sedis abundance per sample:", round(mean(incertae_totals), 2), "%\n")
  cat("- Range:", round(min(incertae_totals), 2), "% to", round(max(incertae_totals), 2), "%\n")
  
  # Calculate family totals
  family_abundance <- data.frame(
    Family = taxonomy_matched$Family,
    rel_abundance,
    stringsAsFactors = FALSE
  )
  
  family_summary <- family_abundance %>%
    group_by(Family) %>%
    summarise(across(everything(), sum), .groups = 'drop')
  
  # Calculate total abundance across all samples
  family_totals <- family_summary %>%
    select(-Family) %>%
    rowSums()
  
  family_ranking <- data.frame(
    Family = family_summary$Family,
    Total_Abundance = family_totals
  ) %>%
    arrange(desc(Total_Abundance))
  
  # Find Incertae sedis rank
  incertae_rank <- which(family_ranking$Family %in% incertae_patterns)
  incertae_total <- family_ranking$Total_Abundance[incertae_rank]
  
  cat("- Incertae sedis rank among all families:", incertae_rank, "\n")
  cat("- Incertae sedis total abundance:", round(incertae_total, 2), "%\n")
  
  # Show top 25 families for context
  cat("\nTop 25 families (for context):\n")
  for (i in 1:min(25, nrow(family_ranking))) {
    family_name <- family_ranking$Family[i]
    abundance <- round(family_ranking$Total_Abundance[i], 2)
    if (family_name %in% incertae_patterns) {
      cat(i, ". ", family_name, " - ", abundance, "% *** INCERTAE SEDIS ***\n", sep = "")
    } else {
      cat(i, ". ", family_name, " - ", abundance, "%\n", sep = "")
    }
  }
  
} else {
  cat("❌ No Incertae sedis found in bacterial families\n")
  cat("Checking for similar terms...\n")
  
  # Check for similar terms
  family_unique <- unique(family_col)
  family_unique <- family_unique[!is.na(family_unique)]
  
  incertae_like <- family_unique[grepl("incertae|sedis|uncertain|unknown", family_unique, ignore.case = TRUE)]
  
  if (length(incertae_like) > 0) {
    cat("Found similar terms:\n")
    for (term in incertae_like) {
      cat("- ", term, "\n")
    }
  } else {
    cat("No similar terms found\n")
  }
}

cat("\n=== VERIFICATION COMPLETE ===\n")
cat("The fix moved Incertae sedis to Others and shows top 20 families excluding it.\n")
cat("File created: taxonomic_barplot_family_bacteria_FIXED_no_incertae.pdf\n")
