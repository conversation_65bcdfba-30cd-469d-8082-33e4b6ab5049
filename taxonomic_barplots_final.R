# Taxonomic Barplots Script
# Create barplots of top 20 classes and families for each dataset
# Show abundance as percentages with sediment and biofilm sites

library(readxl)
library(ggplot2)
library(dplyr)
library(tidyr)
library(RColorBrewer)
library(gridExtra)
library(scales)

# Function to create taxonomic barplots
create_taxonomic_barplots <- function(group_name) {
  cat("=== CREATING TAXONOMIC BARPLOTS:", toupper(group_name), "===\n")
  
  # Read data
  otu_table <- read_excel(paste0("OTU_table_", group_name, ".xlsx"))
  metadata <- read_excel(paste0("metadata_", group_name, ".xlsx"))
  taxonomy <- read_excel(paste0("taxonomy_", group_name, ".xlsx"))
  
  # Prepare matrices
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples
  common_samples <- intersect(colnames(count_matrix), metadata$<PERSON>ple)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Match taxonomy
  common_otus <- intersect(rownames(count_matrix), taxonomy[[1]])
  count_matrix <- count_matrix[common_otus, ]
  taxonomy <- taxonomy[taxonomy[[1]] %in% common_otus, ]
  taxonomy <- taxonomy[match(rownames(count_matrix), taxonomy[[1]]), ]
  
  cat("Data dimensions:", nrow(count_matrix), "OTUs x", ncol(count_matrix), "samples\n")
  
  # Create site labels
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  metadata$SiteLabel <- ifelse(
    metadata$Substrate == "Biofilm",
    paste0("BF", metadata$SiteNumber),
    paste0("SED", metadata$SiteNumber)
  )
  
  # Convert to relative abundance
  rel_abundance <- sweep(count_matrix, 2, colSums(count_matrix), "/") * 100
  
  # Function to aggregate by site and create barplot data
  create_barplot_data <- function(tax_level, top_n = 20) {
    cat("Processing", tax_level, "level...\n")
    
    # Get taxonomy column
    if (tax_level == "Class") {
      tax_col <- "Class"
    } else if (tax_level == "Family") {
      tax_col <- "Family"
    }
    
    # Handle missing taxonomy
    tax_vector <- taxonomy[[tax_col]]
    tax_vector[is.na(tax_vector) | tax_vector == "" | tax_vector == "unidentified"] <- "Unassigned"
    
    # Aggregate by taxonomy
    tax_abundance <- data.frame(
      Taxonomy = tax_vector,
      rel_abundance,
      stringsAsFactors = FALSE
    )
    
    # Sum by taxonomy
    tax_summary <- tax_abundance %>%
      group_by(Taxonomy) %>%
      summarise(across(everything(), sum), .groups = 'drop')
    
    # Convert back to matrix
    tax_matrix <- as.matrix(tax_summary[, -1])
    rownames(tax_matrix) <- tax_summary$Taxonomy
    
    # Aggregate by site (mean of replicates)
    site_abundance <- data.frame()
    
    for (site in unique(metadata$SiteLabel)) {
      site_samples <- metadata$Sample[metadata$SiteLabel == site]
      site_indices <- match(site_samples, colnames(tax_matrix))
      
      if (length(site_indices) > 1) {
        site_mean <- rowMeans(tax_matrix[, site_indices, drop = FALSE])
      } else {
        site_mean <- tax_matrix[, site_indices]
      }
      
      site_data <- data.frame(
        Site = site,
        Substrate = metadata$Substrate[metadata$SiteLabel == site][1],
        Taxonomy = names(site_mean),
        Abundance = as.numeric(site_mean),
        stringsAsFactors = FALSE
      )
      
      site_abundance <- rbind(site_abundance, site_data)
    }
    
    # Calculate total abundance per taxonomy across all sites
    tax_totals <- site_abundance %>%
      group_by(Taxonomy) %>%
      summarise(Total = sum(Abundance), .groups = 'drop') %>%
      arrange(desc(Total))
    
    # Determine top taxa
    available_taxa <- nrow(tax_totals)
    actual_top_n <- min(top_n, available_taxa)
    
    if (available_taxa < top_n) {
      cat("Only", available_taxa, "taxa available (less than", top_n, "requested)\n")
    }
    
    # Get top taxa (excluding Unassigned for ranking)
    top_taxa <- tax_totals %>%
      filter(Taxonomy != "Unassigned") %>%
      head(actual_top_n - 1) %>%  # Reserve one spot for "Others"
      pull(Taxonomy)
    
    # Create "Others" category
    site_abundance_final <- site_abundance %>%
      mutate(
        Taxonomy_grouped = case_when(
          Taxonomy == "Unassigned" ~ "Unassigned",
          Taxonomy %in% top_taxa ~ Taxonomy,
          TRUE ~ "Others"
        )
      ) %>%
      group_by(Site, Substrate, Taxonomy_grouped) %>%
      summarise(Abundance = sum(Abundance), .groups = 'drop') %>%
      rename(Taxonomy = Taxonomy_grouped)
    
    # Ensure all sites have all taxonomy categories (fill with 0 if missing)
    all_sites <- unique(site_abundance_final$Site)
    all_taxa <- unique(site_abundance_final$Taxonomy)

    complete_data <- expand.grid(
      Site = all_sites,
      Taxonomy = all_taxa,
      stringsAsFactors = FALSE
    ) %>%
      left_join(
        site_abundance_final %>% select(Site, Substrate) %>% distinct(),
        by = "Site"
      ) %>%
      left_join(site_abundance_final, by = c("Site", "Taxonomy", "Substrate")) %>%
      mutate(Abundance = ifelse(is.na(Abundance), 0, Abundance))

    # Ensure each site sums to exactly 100% (fix rounding errors)
    # This is critical for proper visualization
    complete_data <- complete_data %>%
      group_by(Site) %>%
      mutate(
        Total = sum(Abundance, na.rm = TRUE),
        # Only normalize if total is greater than 0 and not already 100
        Abundance = ifelse(Total > 0 & abs(Total - 100) > 0.001,
                          (Abundance / Total) * 100,
                          Abundance)
      ) %>%
      ungroup() %>%
      select(-Total)

    # Double-check: force exact 100% for each site
    complete_data <- complete_data %>%
      group_by(Site) %>%
      mutate(
        Site_Total = sum(Abundance, na.rm = TRUE),
        # If still not exactly 100%, adjust the largest category
        Abundance = ifelse(row_number() == which.max(Abundance) & abs(Site_Total - 100) > 0.001,
                          Abundance + (100 - Site_Total),
                          Abundance)
      ) %>%
      ungroup() %>%
      select(-Site_Total)
    
    # Order sites properly
    complete_data$Site <- factor(complete_data$Site, 
                                levels = c(paste0("SED", 1:7), paste0("BF", 1:7)))
    
    # Order taxonomy for plotting (most abundant first, Unassigned and Others last)
    tax_order <- tax_totals %>%
      filter(!Taxonomy %in% c("Unassigned", "Others")) %>%
      head(actual_top_n - 1) %>%
      pull(Taxonomy)
    
    tax_order <- c(tax_order, "Others", "Unassigned")
    complete_data$Taxonomy <- factor(complete_data$Taxonomy, levels = rev(tax_order))
    
    cat("Top", length(tax_order), "taxa (including Others and Unassigned)\n")

    # Verify that all sites sum to 100%
    site_totals <- complete_data %>%
      group_by(Site) %>%
      summarise(Total = sum(Abundance, na.rm = TRUE), .groups = 'drop')

    cat("Site totals verification (", tax_level, "):\n")
    all_perfect <- TRUE
    for (i in 1:nrow(site_totals)) {
      total <- round(site_totals$Total[i], 6)  # More precision for checking
      if (abs(total - 100) > 0.001) {
        cat("⚠️  Site", site_totals$Site[i], "sums to", total, "% (ERROR!)\n")
        all_perfect <- FALSE
      } else {
        cat("✓ Site", site_totals$Site[i], "sums to", round(total, 2), "%\n")
      }
    }

    if (!all_perfect) {
      cat("❌ FIXING REMAINING ISSUES...\n")
      # Final emergency fix: ensure exactly 100%
      complete_data <- complete_data %>%
        group_by(Site) %>%
        mutate(
          Current_Total = sum(Abundance, na.rm = TRUE),
          # Adjust the largest abundance to make total exactly 100
          Abundance = ifelse(row_number() == which.max(Abundance),
                            Abundance + (100 - Current_Total),
                            Abundance)
        ) %>%
        ungroup() %>%
        select(-Current_Total)

      # Re-verify
      site_totals_fixed <- complete_data %>%
        group_by(Site) %>%
        summarise(Total = sum(Abundance, na.rm = TRUE), .groups = 'drop')

      cat("After emergency fix:\n")
      for (i in 1:nrow(site_totals_fixed)) {
        total <- round(site_totals_fixed$Total[i], 2)
        cat("✓ Site", site_totals_fixed$Site[i], "now sums to", total, "%\n")
      }
    }
    
    return(list(
      data = complete_data,
      n_taxa = length(tax_order),
      tax_level = tax_level
    ))
  }
  
  # Create data for both taxonomic levels
  class_data <- create_barplot_data("Class", 20)
  family_data <- create_barplot_data("Family", 20)
  
  # Function to create the actual plot
  create_plot <- function(plot_data, title_suffix) {
    # Create color palette
    n_colors <- plot_data$n_taxa
    if (n_colors <= 12) {
      colors <- brewer.pal(max(3, n_colors), "Set3")
    } else {
      colors <- c(brewer.pal(12, "Set3"), 
                  brewer.pal(min(8, n_colors - 12), "Dark2"))
    }
    
    # Ensure we have enough colors
    if (length(colors) < n_colors) {
      colors <- rep(colors, length.out = n_colors)
    }
    
    # Assign specific colors to special categories
    tax_levels <- levels(plot_data$data$Taxonomy)
    color_mapping <- setNames(colors[1:length(tax_levels)], tax_levels)
    
    # Set specific colors for special categories
    if ("Unassigned" %in% tax_levels) {
      color_mapping["Unassigned"] <- "gray80"
    }
    if ("Others" %in% tax_levels) {
      color_mapping["Others"] <- "gray60"
    }
    
    # Create the plot
    p <- ggplot(plot_data$data, aes(x = Site, y = Abundance, fill = Taxonomy)) +
      geom_bar(stat = "identity", color = "white", size = 0.2) +
      scale_fill_manual(values = color_mapping) +
      scale_y_continuous(labels = function(x) paste0(x, "%"), 
                        limits = c(0, 100),
                        expand = c(0, 0)) +
      labs(
        title = paste("Top", plot_data$n_taxa, plot_data$tax_level, "Composition -", 
                     toupper(group_name), title_suffix),
        subtitle = "Abundance as percentage (mean of replicates per site)",
        x = "Site",
        y = "Relative Abundance (%)",
        fill = plot_data$tax_level
      ) +
      theme_minimal() +
      theme(
        plot.title = element_text(size = 14, face = "bold"),
        plot.subtitle = element_text(size = 12),
        axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
        axis.text.y = element_text(size = 10),
        axis.title = element_text(size = 12),
        legend.title = element_text(size = 12, face = "bold"),
        legend.text = element_text(size = 10),
        legend.position = "right",
        panel.grid.major.x = element_blank(),
        panel.grid.minor = element_blank(),
        strip.text = element_text(size = 12, face = "bold")
      ) +
      guides(fill = guide_legend(ncol = 1, reverse = TRUE))
    
    # Add substrate labels
    p <- p + 
      annotate("text", x = 4, y = 105, label = "SEDIMENT", 
               size = 4, fontface = "bold", color = "darkblue") +
      annotate("text", x = 11, y = 105, label = "BIOFILM", 
               size = 4, fontface = "bold", color = "darkred")
    
    return(p)
  }
  
  # Create plots
  class_plot <- create_plot(class_data, "")
  family_plot <- create_plot(family_data, "")
  
  # Save individual plots as PDF
  ggsave(paste0("taxonomic_barplot_class_", group_name, ".pdf"),
         class_plot, width = 14, height = 10, device = "pdf")
  ggsave(paste0("taxonomic_barplot_family_", group_name, ".pdf"),
         family_plot, width = 14, height = 10, device = "pdf")

  # Create combined plot
  combined_plot <- grid.arrange(
    class_plot, family_plot,
    ncol = 1,
    top = paste("Taxonomic Composition -", toupper(group_name))
  )

  ggsave(paste0("taxonomic_barplot_combined_", group_name, ".pdf"),
         combined_plot, width = 14, height = 16, device = "pdf")
  
  cat("Files saved:\n")
  cat("- taxonomic_barplot_class_", group_name, ".pdf\n")
  cat("- taxonomic_barplot_family_", group_name, ".pdf\n")
  cat("- taxonomic_barplot_combined_", group_name, ".pdf\n\n")
  
  return(list(
    class_plot = class_plot,
    family_plot = family_plot,
    combined_plot = combined_plot,
    class_data = class_data$data,
    family_data = family_data$data
  ))
}

# Main execution
cat("TAXONOMIC BARPLOTS ANALYSIS\n")
cat("===========================\n")
cat("Creating barplots for top 20 classes and families\n")
cat("Showing abundance as percentages with sediment and biofilm sites\n\n")

# Run analysis for all groups
bacteria_plots <- create_taxonomic_barplots("bacteria")
fungi_plots <- create_taxonomic_barplots("fungi")
archaea_plots <- create_taxonomic_barplots("archaea")

cat("=== TAXONOMIC BARPLOTS COMPLETE ===\n")
cat("Generated files:\n")
cat("- Individual class and family plots for each group\n")
cat("- Combined plots showing both taxonomic levels\n")
cat("- All plots show percentages summing to 100%\n")
cat("- Includes 'Others' and 'Unassigned' categories\n")
cat("- Separate bars for sediment and biofilm sites\n")

cat("\n🎯 TAXONOMIC BARPLOTS ANALYSIS COMPLETE! 🎯\n")
