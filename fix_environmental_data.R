# Fix Environmental Data Encoding and Format
# Clean up the environmental data for proper analysis

library(readr)
library(dplyr)

# Read the data with proper encoding
cat("Reading environmental data with encoding handling...\n")

# Try different encodings
env_data <- tryCatch({
  read_delim("environmental_data.csv", delim = ";", locale = locale(decimal_mark = ",", encoding = "UTF-8"))
}, error = function(e) {
  tryCatch({
    read_delim("environmental_data.csv", delim = ";", locale = locale(decimal_mark = ",", encoding = "latin1"))
  }, error = function(e2) {
    read_delim("environmental_data.csv", delim = ";", locale = locale(decimal_mark = ",", encoding = "windows-1252"))
  })
})

cat("Original data loaded with", nrow(env_data), "rows and", ncol(env_data), "columns\n")
print(colnames(env_data))

# Clean column names (remove special characters and spaces)
clean_names <- function(names) {
  names <- gsub("[à<PERSON><PERSON><PERSON><PERSON><PERSON>]", "a", names, ignore.case = TRUE)
  names <- gsub("[èéêë]", "e", names, ignore.case = TRUE)
  names <- gsub("[ìíîï]", "i", names, ignore.case = TRUE)
  names <- gsub("[òóôõö]", "o", names, ignore.case = TRUE)
  names <- gsub("[ùúûü]", "u", names, ignore.case = TRUE)
  names <- gsub("[ýÿ]", "y", names, ignore.case = TRUE)
  names <- gsub("[ç]", "c", names, ignore.case = TRUE)
  names <- gsub("[ñ]", "n", names, ignore.case = TRUE)
  names <- gsub("\\s+", "_", names)  # Replace spaces with underscores
  names <- gsub("[^A-Za-z0-9_]", "", names)  # Remove special characters
  names <- gsub("_+", "_", names)  # Replace multiple underscores with single
  names <- gsub("^_|_$", "", names)  # Remove leading/trailing underscores
  return(names)
}

# Clean column names
colnames(env_data) <- clean_names(colnames(env_data))

cat("Cleaned column names:\n")
print(colnames(env_data))

# Select key environmental variables (remove redundant or problematic ones)
# Keep the most important and commonly used variables
key_vars <- c("Site", "pH", "Conduttivita_mS_cm", "Temperatura_C", 
              "Ossigeno_disciolto_ppm", "Potenziale_redox_mV", 
              "Al", "As", "B", "Ba", "Ca", "Cu", "Fe", "K", "Mg", "Mn", "Ni", "Sr", "V", "Si")

# Check which variables exist
available_vars <- intersect(key_vars, colnames(env_data))
cat("Available key variables:", length(available_vars), "\n")
print(available_vars)

# Select available variables
env_clean <- env_data[, available_vars, drop = FALSE]

# Convert all non-Site columns to numeric
for (col in colnames(env_clean)) {
  if (col != "Site") {
    env_clean[[col]] <- as.numeric(env_clean[[col]])
  }
}

# Check for missing values
missing_summary <- sapply(env_clean, function(x) sum(is.na(x)))
cat("\nMissing values per column:\n")
print(missing_summary)

# Remove columns with too many missing values (>50%)
keep_cols <- names(missing_summary)[missing_summary <= nrow(env_clean) * 0.5]
env_final <- env_clean[, keep_cols, drop = FALSE]

cat("\nFinal dataset:", nrow(env_final), "rows,", ncol(env_final), "columns\n")
print(colnames(env_final))

# Show summary statistics
cat("\nSummary statistics:\n")
print(summary(env_final))

# Save cleaned data
write.csv(env_final, "environmental_data_clean.csv", row.names = FALSE)
cat("\nCleaned data saved as 'environmental_data_clean.csv'\n")

# Create a simplified version with just the most important variables
if (ncol(env_final) > 10) {
  # Select top variables based on importance and completeness
  important_vars <- c("Site", "pH", "Temperatura_C", "Conduttivita_mS_cm", 
                     "Ossigeno_disciolto_ppm", "Ca", "Mg", "K", "Fe", "Mn")
  
  available_important <- intersect(important_vars, colnames(env_final))
  env_simple <- env_final[, available_important, drop = FALSE]
  
  write.csv(env_simple, "environmental_data_simple.csv", row.names = FALSE)
  cat("Simplified data (", ncol(env_simple), "variables) saved as 'environmental_data_simple.csv'\n")
}

cat("\n=== DATA CLEANING COMPLETE ===\n")
cat("You can now run the environmental analysis with the cleaned data!\n")
