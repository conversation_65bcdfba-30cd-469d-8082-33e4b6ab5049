# Complete Publication Analysis for All Three Microbial Groups
# Generate comprehensive figures for bacteria, fungi, and archaea

# Load required libraries
library(readxl)
library(vegan)
library(ggplot2)
library(dplyr)
library(tidyr)
library(gridExtra)
library(RColorBrewer)
library(ape)

# Source the functions from previous script
source("publication_analyses.R")

# Function to create summary table across all groups
create_summary_table <- function(bacteria_results, fungi_results, archaea_results) {
  
  # Extract key statistics
  summary_data <- data.frame(
    Group = c("Bacteria", "Fungi", "Archaea"),
    
    # Sample sizes
    Total_Samples = c(
      nrow(bacteria_results$diversity$data),
      nrow(fungi_results$diversity$data),
      nrow(archaea_results$diversity$data)
    ),
    
    Biofilm_Samples = c(
      sum(bacteria_results$diversity$data$Substrate == "Biofilm"),
      sum(fungi_results$diversity$data$Substrate == "Biofilm"),
      sum(archaea_results$diversity$data$Substrate == "Biofilm")
    ),
    
    Sediment_Samples = c(
      sum(bacteria_results$diversity$data$Substrate == "Sediment"),
      sum(fungi_results$diversity$data$Substrate == "Sediment"),
      sum(archaea_results$diversity$data$Substrate == "Sediment")
    ),
    
    # PERMANOVA results
    Substrate_R2 = c(
      round(bacteria_results$ordination$permanova$substrate$R2[1], 4),
      round(fungi_results$ordination$permanova$substrate$R2[1], 4),
      round(archaea_results$ordination$permanova$substrate$R2[1], 4)
    ),
    
    Substrate_pvalue = c(
      round(bacteria_results$ordination$permanova$substrate$`Pr(>F)`[1], 4),
      round(fungi_results$ordination$permanova$substrate$`Pr(>F)`[1], 4),
      round(archaea_results$ordination$permanova$substrate$`Pr(>F)`[1], 4)
    ),
    
    Site_R2 = c(
      round(bacteria_results$ordination$permanova$site$R2[1], 4),
      round(fungi_results$ordination$permanova$site$R2[1], 4),
      round(archaea_results$ordination$permanova$site$R2[1], 4)
    ),
    
    Site_pvalue = c(
      round(bacteria_results$ordination$permanova$site$`Pr(>F)`[1], 4),
      round(fungi_results$ordination$permanova$site$`Pr(>F)`[1], 4),
      round(archaea_results$ordination$permanova$site$`Pr(>F)`[1], 4)
    ),
    
    # Dispersion test
    Dispersion_pvalue = c(
      round(bacteria_results$dispersion$test$tab$`Pr(>F)`[1], 4),
      round(fungi_results$dispersion$test$tab$`Pr(>F)`[1], 4),
      round(archaea_results$dispersion$test$tab$`Pr(>F)`[1], 4)
    ),
    
    stringsAsFactors = FALSE
  )
  
  return(summary_data)
}

# Function to create combined figure across all groups
create_combined_figure <- function(bacteria_results, fungi_results, archaea_results) {
  
  # Extract diversity plots
  div_bacteria <- bacteria_results$diversity$plot + ggtitle("Alpha Diversity - BACTERIA")
  div_fungi <- fungi_results$diversity$plot + ggtitle("Alpha Diversity - FUNGI")
  div_archaea <- archaea_results$diversity$plot + ggtitle("Alpha Diversity - ARCHAEA")
  
  # Extract ordination plots
  ord_bacteria <- bacteria_results$ordination$plots$substrate + ggtitle("PCoA - BACTERIA")
  ord_fungi <- fungi_results$ordination$plots$substrate + ggtitle("PCoA - FUNGI")
  ord_archaea <- archaea_results$ordination$plots$substrate + ggtitle("PCoA - ARCHAEA")
  
  # Extract beta diversity plots
  beta_bacteria <- bacteria_results$beta_diversity$plot + ggtitle("Beta Diversity - BACTERIA")
  beta_fungi <- fungi_results$beta_diversity$plot + ggtitle("Beta Diversity - FUNGI")
  beta_archaea <- archaea_results$beta_diversity$plot + ggtitle("Beta Diversity - ARCHAEA")
  
  # Create combined figure
  combined_plot <- grid.arrange(
    div_bacteria, div_fungi, div_archaea,
    ord_bacteria, ord_fungi, ord_archaea,
    beta_bacteria, beta_fungi, beta_archaea,
    ncol = 3, nrow = 3
  )
  
  return(combined_plot)
}

# Function to create variance partitioning plot
create_variance_plot <- function(summary_table) {
  
  # Prepare data for plotting
  variance_data <- summary_table %>%
    select(Group, Substrate_R2, Site_R2) %>%
    mutate(Residual = 1 - Substrate_R2 - Site_R2) %>%
    pivot_longer(cols = c(Substrate_R2, Site_R2, Residual), 
                 names_to = "Component", values_to = "Variance") %>%
    mutate(Component = case_when(
      Component == "Substrate_R2" ~ "Substrate",
      Component == "Site_R2" ~ "Site", 
      Component == "Residual" ~ "Residual"
    ))
  
  # Create stacked bar plot
  p_variance <- ggplot(variance_data, aes(x = Group, y = Variance, fill = Component)) +
    geom_col(position = "stack") +
    scale_fill_manual(values = c("Substrate" = "darkblue", "Site" = "darkred", "Residual" = "gray70")) +
    labs(title = "Variance Partitioning Across Microbial Groups",
         x = "Microbial Group", y = "Proportion of Variance Explained",
         fill = "Component") +
    theme_minimal() +
    theme(axis.text.x = element_text(angle = 45, hjust = 1))
  
  return(p_variance)
}

# Main execution
cat("COMPLETE PUBLICATION ANALYSIS FOR ALL GROUPS\n")
cat("============================================\n\n")

# Run comprehensive analysis for all groups
cat("Analyzing BACTERIA...\n")
bacteria_results <- comprehensive_analysis("bacteria")

cat("\nAnalyzing FUNGI...\n")
fungi_results <- comprehensive_analysis("fungi")

cat("\nAnalyzing ARCHAEA...\n")
archaea_results <- comprehensive_analysis("archaea")

# Create summary table
cat("\n=== CREATING SUMMARY TABLE ===\n")
summary_table <- create_summary_table(bacteria_results, fungi_results, archaea_results)
print(summary_table)

# Save summary table
write.csv(summary_table, "publication_summary_table.csv", row.names = FALSE)

# Create combined figure
cat("\n=== CREATING COMBINED FIGURES ===\n")
combined_figure <- create_combined_figure(bacteria_results, fungi_results, archaea_results)

# Save combined figure
ggsave("combined_publication_figure.png", combined_figure, 
       width = 18, height = 15, dpi = 300)

# Create variance partitioning plot
variance_plot <- create_variance_plot(summary_table)
ggsave("variance_partitioning.png", variance_plot, 
       width = 10, height = 6, dpi = 300)

# Create individual ordination comparison
ordination_comparison <- grid.arrange(
  bacteria_results$ordination$plots$substrate + ggtitle("BACTERIA") + theme(legend.position = "bottom"),
  fungi_results$ordination$plots$substrate + ggtitle("FUNGI") + theme(legend.position = "bottom"),
  archaea_results$ordination$plots$substrate + ggtitle("ARCHAEA") + theme(legend.position = "bottom"),
  ncol = 3
)

ggsave("ordination_comparison.png", ordination_comparison, 
       width = 18, height = 6, dpi = 300)

# Create diversity comparison
diversity_comparison <- grid.arrange(
  bacteria_results$diversity$plot + ggtitle("BACTERIA"),
  fungi_results$diversity$plot + ggtitle("FUNGI"), 
  archaea_results$diversity$plot + ggtitle("ARCHAEA"),
  ncol = 1
)

ggsave("diversity_comparison.png", diversity_comparison, 
       width = 12, height = 15, dpi = 300)

cat("\n=== FILES GENERATED ===\n")
cat("1. Individual group figures:\n")
cat("   - publication_figure_bacteria.png\n")
cat("   - publication_figure_fungi.png\n") 
cat("   - publication_figure_archaea.png\n")
cat("   - pcoa_substrate_bacteria.png\n")
cat("   - pcoa_substrate_fungi.png\n")
cat("   - pcoa_substrate_archaea.png\n")
cat("   - pcoa_site_bacteria.png\n")
cat("   - pcoa_site_fungi.png\n")
cat("   - pcoa_site_archaea.png\n")

cat("\n2. Comparative figures:\n")
cat("   - combined_publication_figure.png (3x3 grid)\n")
cat("   - ordination_comparison.png (side-by-side PCoA)\n")
cat("   - diversity_comparison.png (stacked diversity plots)\n")
cat("   - variance_partitioning.png (variance explained)\n")

cat("\n3. Data tables:\n")
cat("   - publication_summary_table.csv\n")

cat("\n=== ANALYSIS COMPLETE ===\n")
cat("All publication-ready materials generated for bacteria, fungi, and archaea!\n")
