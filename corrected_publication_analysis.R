# Corrected Publication Analysis with Proper Site Labeling
# Fix the site labeling issue where biofilm and sediment sites are independent

library(readxl)
library(vegan)
library(ggplot2)
library(dplyr)
library(tidyr)
library(gridExtra)
library(RColorBrewer)
library(ape)

# Function to prepare data with corrected site labels
prepare_corrected_data <- function(group_name) {
  # Read data
  otu_table <- read_excel(paste0("OTU_table_", group_name, ".xlsx"))
  metadata <- read_excel(paste0("metadata_", group_name, ".xlsx"))
  
  # Prepare matrices
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Create corrected site labels
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  metadata$CorrectedSite <- paste(metadata$Substrate, metadata$SiteNumber, sep = "_Site")
  
  # Create a more descriptive site label
  metadata$SiteLabel <- ifelse(
    metadata$Substrate == "Biofilm",
    paste0("BF", metadata$SiteNumber),  # BF1, BF2 for biofilm sites
    paste0("SED", metadata$SiteNumber)  # SED1, SED2, ..., SED7 for sediment sites
  )
  
  # Print site summary
  cat("Site distribution for", toupper(group_name), ":\n")
  site_summary <- metadata %>%
    group_by(SiteLabel, Substrate) %>%
    summarise(n_samples = n(), .groups = "drop") %>%
    arrange(Substrate, SiteLabel)
  print(site_summary)
  cat("\n")
  
  # Transpose for vegan (samples as rows)
  community_matrix <- t(count_matrix)
  
  return(list(
    community = community_matrix,
    metadata = metadata,
    raw_counts = count_matrix
  ))
}

# Updated diversity analysis function
analyze_diversity_corrected <- function(community_matrix, metadata, group_name) {
  cat("=== DIVERSITY ANALYSIS:", toupper(group_name), "===\n")
  
  # Calculate diversity indices
  diversity_data <- data.frame(
    Sample = rownames(community_matrix),
    Richness = specnumber(community_matrix),
    Shannon = diversity(community_matrix, index = "shannon"),
    Simpson = diversity(community_matrix, index = "simpson"),
    Evenness = diversity(community_matrix, index = "shannon") / log(specnumber(community_matrix))
  )
  
  # Merge with metadata
  diversity_data <- merge(diversity_data, metadata, by = "Sample")
  
  # Statistical tests
  cat("Alpha diversity comparisons (Wilcoxon tests):\n")
  for (index in c("Richness", "Shannon", "Simpson", "Evenness")) {
    biofilm_vals <- diversity_data[[index]][diversity_data$Substrate == "Biofilm"]
    sediment_vals <- diversity_data[[index]][diversity_data$Substrate == "Sediment"]
    
    test_result <- wilcox.test(biofilm_vals, sediment_vals)
    cat(index, "p-value:", round(test_result$p.value, 4), "\n")
  }
  
  # Create diversity plots with corrected site labels
  diversity_long <- diversity_data %>%
    select(Sample, Substrate, SiteLabel, Richness, Shannon, Simpson, Evenness) %>%
    pivot_longer(cols = c(Richness, Shannon, Simpson, Evenness), 
                 names_to = "Index", values_to = "Value")
  
  p_diversity <- ggplot(diversity_long, aes(x = Substrate, y = Value, fill = Substrate)) +
    geom_boxplot(alpha = 0.7) +
    geom_jitter(aes(shape = SiteLabel), width = 0.2, alpha = 0.8, size = 2) +
    facet_wrap(~Index, scales = "free_y") +
    scale_fill_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    scale_shape_manual(values = c(15:25)) +  # Different shapes for different sites
    labs(title = paste("Alpha Diversity -", toupper(group_name)),
         x = "Substrate", y = "Diversity Value", shape = "Site") +
    theme_minimal() +
    theme(legend.position = "right")
  
  return(list(data = diversity_data, plot = p_diversity))
}

# Updated ordination analysis function
analyze_ordination_corrected <- function(community_matrix, metadata, group_name) {
  cat("=== ORDINATION ANALYSIS:", toupper(group_name), "===\n")
  
  # Remove empty samples/species
  community_matrix <- community_matrix[rowSums(community_matrix) > 0, ]
  community_matrix <- community_matrix[, colSums(community_matrix) > 0]
  
  # Calculate distance matrix
  dist_matrix <- vegdist(community_matrix, method = "bray")
  
  # PCoA
  pcoa_result <- pcoa(dist_matrix)
  pcoa_data <- data.frame(
    Sample = rownames(community_matrix),
    PC1 = pcoa_result$vectors[, 1],
    PC2 = pcoa_result$vectors[, 2],
    PC3 = pcoa_result$vectors[, 3]
  )
  
  # Merge with metadata
  pcoa_data <- merge(pcoa_data, metadata, by = "Sample")
  
  # Calculate variance explained
  var_explained <- pcoa_result$values$Relative_eig[1:3] * 100
  
  # PERMANOVA tests - NOTE: We can't test site effects properly now since biofilm and sediment sites are independent
  cat("PERMANOVA results:\n")
  
  # Test substrate effect only
  perm_substrate <- adonis2(dist_matrix ~ Substrate, data = metadata, permutations = 999)
  cat("Substrate effect - R2:", round(perm_substrate$R2[1], 4), 
      "p-value:", round(perm_substrate$`Pr(>F)`[1], 4), "\n")
  
  # Test site effect within sediment samples only (since they have multiple sites)
  sediment_samples <- metadata$Sample[metadata$Substrate == "Sediment"]
  if (length(sediment_samples) > 0) {
    sediment_metadata <- metadata[metadata$Sample %in% sediment_samples, ]
    sediment_dist <- as.dist(as.matrix(dist_matrix)[sediment_samples, sediment_samples])
    
    perm_site_sediment <- adonis2(sediment_dist ~ SiteLabel, data = sediment_metadata, permutations = 999)
    cat("Site effect (sediment only) - R2:", round(perm_site_sediment$R2[1], 4), 
        "p-value:", round(perm_site_sediment$`Pr(>F)`[1], 4), "\n")
  }
  
  # PERMDISP (test for dispersion differences)
  disp_substrate <- betadisper(dist_matrix, metadata$Substrate)
  disp_test <- permutest(disp_substrate, permutations = 999)
  cat("PERMDISP (dispersion) p-value:", round(disp_test$tab$`Pr(>F)`[1], 4), "\n")
  
  # Create ordination plots with corrected labels
  # Define colors for sites
  n_sites <- length(unique(metadata$SiteLabel))
  site_colors <- rainbow(n_sites)
  names(site_colors) <- sort(unique(metadata$SiteLabel))
  
  p1 <- ggplot(pcoa_data, aes(x = PC1, y = PC2)) +
    geom_point(aes(color = Substrate, shape = SiteLabel), size = 3, alpha = 0.8) +
    scale_color_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    scale_shape_manual(values = c(15:25)) +
    labs(title = paste("PCoA - Substrate Effect -", toupper(group_name)),
         x = paste0("PC1 (", round(var_explained[1], 1), "%)"),
         y = paste0("PC2 (", round(var_explained[2], 1), "%)"),
         shape = "Site") +
    theme_minimal() +
    stat_ellipse(aes(color = Substrate), level = 0.68, linetype = "dashed") +
    theme(legend.position = "right")
  
  p2 <- ggplot(pcoa_data, aes(x = PC1, y = PC2)) +
    geom_point(aes(color = SiteLabel, shape = Substrate), size = 3, alpha = 0.8) +
    scale_color_manual(values = site_colors) +
    scale_shape_manual(values = c("Biofilm" = 16, "Sediment" = 17)) +
    labs(title = paste("PCoA - Site Effect -", toupper(group_name)),
         x = paste0("PC1 (", round(var_explained[1], 1), "%)"),
         y = paste0("PC2 (", round(var_explained[2], 1), "%)"),
         color = "Site", shape = "Substrate") +
    theme_minimal() +
    theme(legend.position = "right")
  
  return(list(
    pcoa_data = pcoa_data,
    var_explained = var_explained,
    permanova = list(substrate = perm_substrate),
    permdisp = disp_test,
    plots = list(substrate = p1, site = p2),
    distances = dist_matrix
  ))
}

# Main corrected analysis function
corrected_analysis <- function(group_name) {
  cat("\n", paste(rep("=", 70), collapse = ""), "\n")
  cat("CORRECTED ANALYSIS:", toupper(group_name), "\n")
  cat(paste(rep("=", 70), collapse = ""), "\n")
  
  # Prepare data with corrected site labels
  data <- prepare_corrected_data(group_name)
  
  # Run analyses
  diversity_results <- analyze_diversity_corrected(data$community, data$metadata, group_name)
  ordination_results <- analyze_ordination_corrected(data$community, data$metadata, group_name)
  
  # Create combined plot
  combined_plot <- grid.arrange(
    diversity_results$plot,
    ordination_results$plots$substrate,
    ordination_results$plots$site,
    ncol = 1
  )
  
  # Save results
  ggsave(paste0("corrected_figure_", group_name, ".png"), combined_plot, 
         width = 12, height = 15, dpi = 300)
  
  ggsave(paste0("corrected_pcoa_substrate_", group_name, ".png"), 
         ordination_results$plots$substrate, width = 12, height = 8, dpi = 300)
  
  ggsave(paste0("corrected_pcoa_site_", group_name, ".png"), 
         ordination_results$plots$site, width = 12, height = 8, dpi = 300)
  
  cat("\nFiles saved:\n")
  cat("- Combined figure:", paste0("corrected_figure_", group_name, ".png"), "\n")
  cat("- PCoA substrate:", paste0("corrected_pcoa_substrate_", group_name, ".png"), "\n")
  cat("- PCoA site:", paste0("corrected_pcoa_site_", group_name, ".png"), "\n")
  
  return(list(
    diversity = diversity_results,
    ordination = ordination_results,
    data = data
  ))
}

# Function to create corrected summary table
create_corrected_summary <- function(bacteria_results, fungi_results, archaea_results) {
  summary_data <- data.frame(
    Group = c("Bacteria", "Fungi", "Archaea"),
    
    # Sample sizes
    Total_Samples = c(
      nrow(bacteria_results$diversity$data),
      nrow(fungi_results$diversity$data),
      nrow(archaea_results$diversity$data)
    ),
    
    Biofilm_Samples = c(
      sum(bacteria_results$diversity$data$Substrate == "Biofilm"),
      sum(fungi_results$diversity$data$Substrate == "Biofilm"),
      sum(archaea_results$diversity$data$Substrate == "Biofilm")
    ),
    
    Sediment_Samples = c(
      sum(bacteria_results$diversity$data$Substrate == "Sediment"),
      sum(fungi_results$diversity$data$Substrate == "Sediment"),
      sum(archaea_results$diversity$data$Substrate == "Sediment")
    ),
    
    Biofilm_Sites = c(2, 2, 2),  # Only 2 biofilm sites
    Sediment_Sites = c(7, 7, 7), # 7 sediment sites
    
    # PERMANOVA results (substrate only - site comparison not valid)
    Substrate_R2 = c(
      round(bacteria_results$ordination$permanova$substrate$R2[1], 4),
      round(fungi_results$ordination$permanova$substrate$R2[1], 4),
      round(archaea_results$ordination$permanova$substrate$R2[1], 4)
    ),
    
    Substrate_pvalue = c(
      round(bacteria_results$ordination$permanova$substrate$`Pr(>F)`[1], 4),
      round(fungi_results$ordination$permanova$substrate$`Pr(>F)`[1], 4),
      round(archaea_results$ordination$permanova$substrate$`Pr(>F)`[1], 4)
    ),
    
    # Dispersion test
    Dispersion_pvalue = c(
      round(bacteria_results$ordination$permdisp$tab$`Pr(>F)`[1], 4),
      round(fungi_results$ordination$permdisp$tab$`Pr(>F)`[1], 4),
      round(archaea_results$ordination$permdisp$tab$`Pr(>F)`[1], 4)
    ),
    
    stringsAsFactors = FALSE
  )
  
  return(summary_data)
}

# Run corrected analysis
cat("CORRECTED PUBLICATION ANALYSIS\n")
cat("==============================\n")
cat("Fixing site labeling: Biofilm sites (BF1, BF2) are independent from Sediment sites (SED1-SED7)\n\n")

# Analyze all groups
bacteria_corrected <- corrected_analysis("bacteria")
fungi_corrected <- corrected_analysis("fungi")
archaea_corrected <- corrected_analysis("archaea")

# Create corrected summary
corrected_summary <- create_corrected_summary(bacteria_corrected, fungi_corrected, archaea_corrected)
print(corrected_summary)
write.csv(corrected_summary, "corrected_summary_table.csv", row.names = FALSE)

cat("\n=== CORRECTED ANALYSIS COMPLETE ===\n")
cat("New files generated with proper site labeling:\n")
cat("- corrected_figure_*.png (individual group figures)\n")
cat("- corrected_pcoa_substrate_*.png (substrate PCoA plots)\n") 
cat("- corrected_pcoa_site_*.png (site PCoA plots)\n")
cat("- corrected_summary_table.csv (updated statistical summary)\n")
