# Environmental Correlation Analysis
# Correlating physicochemical parameters with microbial communities
# Proper handling of site-level environmental data

library(readxl)
library(vegan)
library(ggplot2)
library(dplyr)
library(tidyr)
library(corrplot)
library(gridExtra)
library(RColorBrewer)

# =============================================================================
# FUNCTION: Prepare site-aggregated community data
# =============================================================================

prepare_site_aggregated_data <- function(group_name) {
  cat("=== PREPARING SITE-AGGREGATED DATA:", toupper(group_name), "===\n")
  
  # Read data
  otu_table <- read_excel(paste0("OTU_table_", group_name, ".xlsx"))
  metadata <- read_excel(paste0("metadata_", group_name, ".xlsx"))
  
  # Prepare matrices
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Create site labels
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  metadata$SiteLabel <- ifelse(
    metadata$Substrate == "Biofilm",
    paste0("BF", metadata$SiteNumber),
    paste0("SED", metadata$SiteNumber)
  )
  
  # Filter to sediment samples only (since environmental data is for sediment sites)
  sediment_samples <- metadata$Sample[metadata$Substrate == "Sediment"]
  sediment_metadata <- metadata[metadata$Substrate == "Sediment", ]
  sediment_counts <- count_matrix[, sediment_samples]
  
  cat("Sediment samples:", ncol(sediment_counts), "\n")
  cat("Sediment sites:", length(unique(sediment_metadata$SiteLabel)), "\n")
  
  # Convert to relative abundance
  sediment_rel <- sweep(sediment_counts, 2, colSums(sediment_counts), "/")
  
  # Aggregate to site level (mean relative abundance per site)
  site_aggregated <- sediment_metadata %>%
    select(Sample, SiteLabel) %>%
    mutate(SampleIndex = match(Sample, colnames(sediment_rel)))
  
  # Calculate site-level community data
  site_communities <- list()
  site_labels <- unique(sediment_metadata$SiteLabel)
  
  for (site in site_labels) {
    site_samples <- site_aggregated$Sample[site_aggregated$SiteLabel == site]
    site_indices <- match(site_samples, colnames(sediment_rel))
    
    if (length(site_indices) > 1) {
      # Multiple samples per site - take mean
      site_community <- rowMeans(sediment_rel[, site_indices, drop = FALSE])
    } else {
      # Single sample per site
      site_community <- sediment_rel[, site_indices]
    }
    
    site_communities[[site]] <- site_community
  }
  
  # Convert to matrix
  site_matrix <- do.call(cbind, site_communities)
  colnames(site_matrix) <- names(site_communities)
  
  # Remove taxa not present in any site
  site_matrix <- site_matrix[rowSums(site_matrix) > 0, ]
  
  cat("Site-aggregated matrix:", nrow(site_matrix), "taxa x", ncol(site_matrix), "sites\n\n")
  
  return(list(
    site_matrix = site_matrix,
    site_labels = colnames(site_matrix),
    original_metadata = sediment_metadata
  ))
}

# =============================================================================
# FUNCTION: Environmental correlation analysis
# =============================================================================

analyze_environmental_correlations <- function(site_matrix, env_data, group_name) {
  cat("=== ENVIRONMENTAL CORRELATION ANALYSIS:", toupper(group_name), "===\n")
  
  # Ensure site order matches
  common_sites <- intersect(colnames(site_matrix), env_data$Site)
  site_matrix <- site_matrix[, common_sites]
  env_data <- env_data[env_data$Site %in% common_sites, ]
  env_data <- env_data[match(common_sites, env_data$Site), ]
  
  cat("Sites with both community and environmental data:", length(common_sites), "\n")
  print(common_sites)
  
  # Prepare environmental matrix (exclude Site column)
  env_matrix <- env_data[, !colnames(env_data) %in% c("Site"), drop = FALSE]
  rownames(env_matrix) <- env_data$Site
  
  # Remove any non-numeric columns or columns with missing data
  numeric_cols <- sapply(env_matrix, is.numeric)
  env_matrix <- env_matrix[, numeric_cols, drop = FALSE]
  
  # Remove columns with any missing values
  complete_cols <- sapply(env_matrix, function(x) !any(is.na(x)))
  env_matrix <- env_matrix[, complete_cols, drop = FALSE]
  
  cat("Environmental variables included:", ncol(env_matrix), "\n")
  print(colnames(env_matrix))
  
  if (ncol(env_matrix) == 0) {
    cat("ERROR: No suitable environmental variables found!\n")
    return(NULL)
  }
  
  # Transpose community matrix for vegan (sites as rows)
  community_t <- t(site_matrix)
  
  # Calculate distance matrix
  comm_dist <- vegdist(community_t, method = "bray")
  
  # Ordination
  pcoa_result <- pcoa(comm_dist)
  pcoa_scores <- pcoa_result$vectors[, 1:2]
  
  # Environmental fitting
  env_fit <- envfit(pcoa_scores, env_matrix, permutations = 999)
  
  cat("\nEnvironmental fitting results:\n")
  print(env_fit)
  
  # Mantel test (if more than 3 sites)
  if (nrow(env_matrix) > 3) {
    env_dist <- dist(scale(env_matrix))
    mantel_result <- mantel(comm_dist, env_dist, permutations = 999)
    
    cat("\nMantel test (community ~ environment):\n")
    cat("Correlation:", round(mantel_result$statistic, 4), "\n")
    cat("p-value:", round(mantel_result$signif, 4), "\n")
  } else {
    mantel_result <- NULL
    cat("\nMantel test skipped (too few sites)\n")
  }
  
  # RDA (if sufficient sites and variables)
  if (nrow(env_matrix) > ncol(env_matrix) + 1) {
    rda_result <- rda(community_t ~ ., data = env_matrix)
    rda_summary <- summary(rda_result)
    
    cat("\nRDA results:\n")
    print(rda_summary$cont$importance[1:2, 1:2])
    
    # Test significance
    rda_anova <- anova(rda_result, permutations = 999)
    cat("\nRDA significance test:\n")
    print(rda_anova)
  } else {
    rda_result <- NULL
    cat("\nRDA skipped (insufficient degrees of freedom)\n")
  }
  
  return(list(
    env_fit = env_fit,
    mantel = mantel_result,
    rda = rda_result,
    pcoa_scores = pcoa_scores,
    env_matrix = env_matrix,
    community_dist = comm_dist
  ))
}

# =============================================================================
# FUNCTION: Create environmental correlation plots
# =============================================================================

create_environmental_plots <- function(analysis_results, group_name) {
  cat("=== CREATING ENVIRONMENTAL PLOTS:", toupper(group_name), "===\n")
  
  if (is.null(analysis_results)) {
    return(NULL)
  }
  
  # Extract data
  pcoa_scores <- analysis_results$pcoa_scores
  env_matrix <- analysis_results$env_matrix
  env_fit <- analysis_results$env_fit
  
  # Create data frame for plotting
  plot_data <- data.frame(
    Site = rownames(pcoa_scores),
    PC1 = pcoa_scores[, 1],
    PC2 = pcoa_scores[, 2]
  )
  
  # Add environmental data
  plot_data <- cbind(plot_data, env_matrix[rownames(pcoa_scores), ])
  
  # 1. PCoA with environmental vectors
  p1 <- ggplot(plot_data, aes(x = PC1, y = PC2)) +
    geom_point(size = 4, alpha = 0.8, color = "darkred") +
    geom_text(aes(label = Site), vjust = -1, size = 3, fontface = "bold") +
    labs(
      title = paste("Environmental Correlations -", toupper(group_name)),
      subtitle = "PCoA with environmental vectors (sediment sites only)",
      x = "PC1", y = "PC2"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12)
    )
  
  # Add environmental vectors if significant
  if (!is.null(env_fit$vectors)) {
    sig_vectors <- env_fit$vectors$pvals <= 0.1  # Show marginally significant
    if (any(sig_vectors)) {
      vector_coords <- env_fit$vectors$arrows[sig_vectors, , drop = FALSE]
      vector_data <- data.frame(
        Variable = rownames(vector_coords),
        PC1_end = vector_coords[, 1] * 0.8,  # Scale for visibility
        PC2_end = vector_coords[, 2] * 0.8,
        pvalue = env_fit$vectors$pvals[sig_vectors]
      )
      
      p1 <- p1 +
        geom_segment(data = vector_data, 
                     aes(x = 0, y = 0, xend = PC1_end, yend = PC2_end),
                     arrow = arrow(length = unit(0.3, "cm")),
                     color = "blue", size = 1) +
        geom_text(data = vector_data,
                  aes(x = PC1_end * 1.1, y = PC2_end * 1.1, label = Variable),
                  color = "blue", fontface = "bold", size = 3)
    }
  }
  
  # 2. Environmental correlation matrix
  if (ncol(env_matrix) > 1) {
    env_cor <- cor(env_matrix, use = "complete.obs")
    
    # Convert to long format for ggplot
    cor_data <- expand.grid(Var1 = colnames(env_cor), Var2 = colnames(env_cor))
    cor_data$Correlation <- as.vector(env_cor)
    
    p2 <- ggplot(cor_data, aes(x = Var1, y = Var2, fill = Correlation)) +
      geom_tile() +
      geom_text(aes(label = round(Correlation, 2)), size = 3) +
      scale_fill_gradient2(low = "blue", mid = "white", high = "red", 
                           midpoint = 0, limits = c(-1, 1)) +
      labs(
        title = "Environmental Variable Correlations",
        x = "", y = ""
      ) +
      theme_minimal() +
      theme(
        axis.text.x = element_text(angle = 45, hjust = 1),
        plot.title = element_text(size = 14, face = "bold")
      )
  } else {
    p2 <- ggplot() + 
      labs(title = "Correlation matrix requires >1 variable") +
      theme_minimal()
  }
  
  # 3. Environmental gradients along PC axes
  if (ncol(env_matrix) > 0) {
    # Select first environmental variable for demonstration
    env_var <- colnames(env_matrix)[1]
    
    p3 <- ggplot(plot_data, aes_string(x = "PC1", y = "PC2", color = env_var)) +
      geom_point(size = 4, alpha = 0.8) +
      geom_text(aes(label = Site), vjust = -1, size = 3, fontface = "bold") +
      scale_color_gradient(low = "lightblue", high = "darkblue") +
      labs(
        title = paste("Environmental Gradient:", env_var),
        x = "PC1", y = "PC2",
        color = env_var
      ) +
      theme_minimal() +
      theme(
        plot.title = element_text(size = 14, face = "bold")
      )
  } else {
    p3 <- ggplot() + 
      labs(title = "No environmental variables available") +
      theme_minimal()
  }
  
  return(list(vectors = p1, correlation = p2, gradient = p3))
}

# =============================================================================
# FUNCTION: Main environmental analysis
# =============================================================================

environmental_analysis <- function(group_name, env_file) {
  cat("\n", paste(rep("=", 70), collapse = ""), "\n")
  cat("ENVIRONMENTAL CORRELATION ANALYSIS:", toupper(group_name), "\n")
  cat(paste(rep("=", 70), collapse = ""), "\n")
  
  # Read environmental data (try both Excel and CSV formats)
  env_data <- NULL

  if (file.exists(env_file)) {
    if (grepl("\\.xlsx$", env_file)) {
      env_data <- read_excel(env_file)
    } else if (grepl("\\.csv$", env_file)) {
      # Try different CSV formats (comma vs semicolon separated, different decimal separators)
      env_data <- tryCatch({
        read.csv(env_file, stringsAsFactors = FALSE, sep = ",", dec = ".")
      }, error = function(e) {
        tryCatch({
          read.csv(env_file, stringsAsFactors = FALSE, sep = ";", dec = ",")
        }, error = function(e2) {
          read.csv(env_file, stringsAsFactors = FALSE, sep = ";", dec = ".")
        })
      })
    }
    cat("Environmental data loaded:", nrow(env_data), "sites,", ncol(env_data)-1, "variables\n")
    print(head(env_data))
  } else {
    # Try alternative file formats
    csv_file <- gsub("\\.xlsx$", ".csv", env_file)
    xlsx_file <- gsub("\\.csv$", ".xlsx", env_file)

    if (file.exists(csv_file)) {
      # Try different CSV formats
      env_data <- tryCatch({
        read.csv(csv_file, stringsAsFactors = FALSE, sep = ",", dec = ".")
      }, error = function(e) {
        tryCatch({
          read.csv(csv_file, stringsAsFactors = FALSE, sep = ";", dec = ",")
        }, error = function(e2) {
          read.csv(csv_file, stringsAsFactors = FALSE, sep = ";", dec = ".")
        })
      })
      cat("Environmental data loaded from CSV:", nrow(env_data), "sites,", ncol(env_data)-1, "variables\n")
      print(head(env_data))
    } else if (file.exists(xlsx_file)) {
      env_data <- read_excel(xlsx_file)
      cat("Environmental data loaded from Excel:", nrow(env_data), "sites,", ncol(env_data)-1, "variables\n")
      print(head(env_data))
    } else {
      cat("Environmental data file not found:", env_file, "\n")
      cat("Please create this file with columns: Site, [environmental variables]\n")
      cat("Site names should match: SED1, SED2, SED3, SED4, SED5, SED6, SED7\n")
      cat("Supported formats: .xlsx or .csv\n")
      return(NULL)
    }
  }
  
  # Prepare site-aggregated community data
  site_data <- prepare_site_aggregated_data(group_name)
  
  # Perform correlation analysis
  correlation_results <- analyze_environmental_correlations(
    site_data$site_matrix, env_data, group_name
  )
  
  if (!is.null(correlation_results)) {
    # Create plots
    env_plots <- create_environmental_plots(correlation_results, group_name)
    
    # Save plots
    if (!is.null(env_plots)) {
      combined_env_plot <- grid.arrange(
        env_plots$vectors,
        env_plots$correlation,
        env_plots$gradient,
        ncol = 1
      )
      
      ggsave(paste0("environmental_analysis_", group_name, ".png"), 
             combined_env_plot, width = 12, height = 15, dpi = 300)
      
      ggsave(paste0("environmental_vectors_", group_name, ".png"), 
             env_plots$vectors, width = 10, height = 8, dpi = 300)
    }
    
    cat("\nFiles saved:\n")
    cat("- environmental_analysis_", group_name, ".png\n")
    cat("- environmental_vectors_", group_name, ".png\n")
  }
  
  return(list(
    site_data = site_data,
    correlation_results = correlation_results,
    plots = env_plots
  ))
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

cat("ENVIRONMENTAL CORRELATION ANALYSIS\n")
cat("===================================\n")
cat("This analysis correlates physicochemical parameters with microbial communities\n")
cat("at the site level (sediment sites only).\n\n")

cat("REQUIREMENTS:\n")
cat("1. Create 'environmental_data.xlsx' with columns:\n")
cat("   - Site: SED1, SED2, SED3, SED4, SED5, SED6, SED7\n")
cat("   - [Environmental variables]: pH, Temperature, Nutrients, etc.\n")
cat("2. One measurement per site is appropriate for this analysis\n\n")

# Check if environmental data exists (prioritize cleaned data)
env_file_clean <- "environmental_data_clean.csv"
env_file_simple <- "environmental_data_simple.csv"
env_file_xlsx <- "environmental_data.xlsx"
env_file_csv <- "environmental_data.csv"

if (file.exists(env_file_clean)) {
  env_file <- env_file_clean
  cat("Using cleaned environmental data...\n\n")
} else if (file.exists(env_file_simple)) {
  env_file <- env_file_simple
  cat("Using simplified environmental data...\n\n")
} else if (file.exists(env_file_xlsx) || file.exists(env_file_csv)) {
  # Use whichever file exists
  env_file <- ifelse(file.exists(env_file_xlsx), env_file_xlsx, env_file_csv)
  cat("Environmental data file found. Running analysis...\n\n")
  
  # Run analysis for all groups
  bacteria_env <- environmental_analysis("bacteria", env_file)
  fungi_env <- environmental_analysis("fungi", env_file)
  archaea_env <- environmental_analysis("archaea", env_file)
  
  cat("\n", paste(rep("=", 70), collapse = ""), "\n")
  cat("ENVIRONMENTAL ANALYSIS COMPLETE\n")
  cat(paste(rep("=", 70), collapse = ""), "\n")
  
} else {
  cat("Environmental data file not found!\n")
  cat("Please create either 'environmental_data.xlsx' or 'environmental_data.csv'\n")
  cat("A template CSV file has been created: 'environmental_data_template.csv'\n")
  cat("You can:\n")
  cat("1. Edit the template CSV file with your data and rename to 'environmental_data.csv'\n")
  cat("2. Open the template in Excel, edit, and save as 'environmental_data.xlsx'\n")
  cat("\nRequired format:\n")
  cat("Site,pH,Temperature,Nitrogen,Phosphorus\n")
  cat("SED1,7.2,15.5,2.3,0.8\n")
  cat("SED2,6.8,16.1,1.9,0.6\n")
  cat("...\n")
}

