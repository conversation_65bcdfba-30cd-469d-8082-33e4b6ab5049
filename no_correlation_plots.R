# No Correlation Demonstration Plots
# Create comprehensive visualizations showing lack of environmental correlation

library(readxl)
library(vegan)
library(ggplot2)
library(dplyr)
library(ape)
library(gridExtra)
library(corrplot)
library(RColorBrewer)

# Function to prepare data for one group
prepare_data <- function(group_name) {
  # Read microbial data
  otu_table <- read_excel(paste0("OTU_table_", group_name, ".xlsx"))
  metadata <- read_excel(paste0("metadata_", group_name, ".xlsx"))
  
  # Prepare community matrix
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Create site labels and filter to sediment
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  metadata$SiteLabel <- paste0("SED", metadata$SiteNumber)
  sediment_samples <- metadata$Sample[metadata$Substrate == "Sediment"]
  sediment_metadata <- metadata[metadata$Substrate == "Sediment", ]
  sediment_counts <- count_matrix[, sediment_samples]
  
  # Convert to relative abundance and aggregate to site level
  sediment_rel <- sweep(sediment_counts, 2, colSums(sediment_counts), "/")
  
  site_communities <- list()
  for (site in unique(sediment_metadata$SiteLabel)) {
    site_samples <- sediment_metadata$Sample[sediment_metadata$SiteLabel == site]
    site_indices <- match(site_samples, colnames(sediment_rel))
    
    if (length(site_indices) > 1) {
      site_community <- rowMeans(sediment_rel[, site_indices, drop = FALSE])
    } else {
      site_community <- sediment_rel[, site_indices]
    }
    site_communities[[site]] <- site_community
  }
  
  # Convert to matrix
  site_matrix <- do.call(cbind, site_communities)
  colnames(site_matrix) <- names(site_communities)
  site_matrix <- site_matrix[rowSums(site_matrix) > 0, ]
  
  return(list(
    site_matrix = site_matrix,
    site_labels = colnames(site_matrix)
  ))
}

# Read environmental data
env_data <- read.csv("environmental_data_clean.csv", stringsAsFactors = FALSE)

# Prepare data for all groups
bacteria_data <- prepare_data("bacteria")
fungi_data <- prepare_data("fungi")
archaea_data <- prepare_data("archaea")

# Match sites with environmental data
common_sites <- intersect(bacteria_data$site_labels, env_data$Site)
env_data <- env_data[env_data$Site %in% common_sites, ]
env_data <- env_data[match(common_sites, env_data$Site), ]

# Prepare environmental matrix
env_matrix <- env_data[, !colnames(env_data) %in% c("Site"), drop = FALSE]
rownames(env_matrix) <- env_data$Site

# Calculate community distances for each group
calc_distances <- function(site_matrix, common_sites) {
  site_matrix <- site_matrix[, common_sites]
  community_t <- t(site_matrix)
  return(vegdist(community_t, method = "bray"))
}

bacteria_dist <- calc_distances(bacteria_data$site_matrix, common_sites)
fungi_dist <- calc_distances(fungi_data$site_matrix, common_sites)
archaea_dist <- calc_distances(archaea_data$site_matrix, common_sites)

# Environmental distance
env_dist <- dist(scale(env_matrix))

# =============================================================================
# PLOT 1: Mantel Test Scatterplots
# =============================================================================

create_mantel_plot <- function(comm_dist, env_dist, group_name, mantel_r, mantel_p) {
  # Convert distance matrices to vectors
  comm_vec <- as.vector(comm_dist)
  env_vec <- as.vector(env_dist)
  
  # Create data frame
  plot_data <- data.frame(
    Environmental_Distance = env_vec,
    Community_Distance = comm_vec
  )
  
  # Create plot
  ggplot(plot_data, aes(x = Environmental_Distance, y = Community_Distance)) +
    geom_point(size = 3, alpha = 0.7, color = "darkblue") +
    geom_smooth(method = "lm", se = TRUE, color = "red", linetype = "dashed") +
    labs(
      title = paste("No Environmental Correlation -", toupper(group_name)),
      subtitle = paste("Mantel r =", round(mantel_r, 3), ", p =", round(mantel_p, 3), "(not significant)"),
      x = "Environmental Distance (scaled)",
      y = "Community Distance (Bray-Curtis)"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12, color = "red"),
      axis.title = element_text(size = 12),
      panel.grid.minor = element_blank()
    ) +
    annotate("text", x = Inf, y = Inf, 
             label = "No significant\ncorrelation", 
             hjust = 1.1, vjust = 1.1, 
             size = 4, color = "red", fontface = "bold")
}

# Calculate Mantel tests
bacteria_mantel <- mantel(bacteria_dist, env_dist, permutations = 999)
fungi_mantel <- mantel(fungi_dist, env_dist, permutations = 999)
archaea_mantel <- mantel(archaea_dist, env_dist, permutations = 999)

# Create Mantel plots
p1 <- create_mantel_plot(bacteria_dist, env_dist, "bacteria", 
                        bacteria_mantel$statistic, bacteria_mantel$signif)
p2 <- create_mantel_plot(fungi_dist, env_dist, "fungi", 
                        fungi_mantel$statistic, fungi_mantel$signif)
p3 <- create_mantel_plot(archaea_dist, env_dist, "archaea", 
                        archaea_mantel$statistic, archaea_mantel$signif)

# =============================================================================
# PLOT 2: Environmental Variable Ranges (showing limited variation)
# =============================================================================

# Select key environmental variables
key_vars <- c("pH", "Temperatura_C", "Ossigeno_disciolto_ppm", "Ca", "Mg", "K")
env_subset <- env_matrix[, key_vars]

# Create range plot
env_ranges <- data.frame(
  Variable = names(env_subset),
  Min = sapply(env_subset, min),
  Max = sapply(env_subset, max),
  Range = sapply(env_subset, function(x) max(x) - min(x)),
  CV = sapply(env_subset, function(x) sd(x)/mean(x) * 100)  # Coefficient of variation
)

env_ranges$Variable <- factor(env_ranges$Variable, levels = env_ranges$Variable)

p4 <- ggplot(env_ranges, aes(x = Variable)) +
  geom_errorbar(aes(ymin = Min, ymax = Max), width = 0.2, size = 1, color = "darkblue") +
  geom_point(aes(y = (Min + Max)/2), size = 4, color = "red") +
  labs(
    title = "Limited Environmental Variation Explains Weak Correlations",
    subtitle = "Error bars show min-max range; points show means",
    x = "Environmental Variables",
    y = "Scaled Values"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12),
    axis.text.x = element_text(angle = 45, hjust = 1),
    panel.grid.minor = element_blank()
  ) +
  annotate("text", x = Inf, y = Inf, 
           label = "Narrow ranges\nlimit correlation\ndetection", 
           hjust = 1.1, vjust = 1.1, 
           size = 4, color = "red", fontface = "bold")

# =============================================================================
# PLOT 3: Summary Statistics Plot
# =============================================================================

# Create summary data
summary_data <- data.frame(
  Group = c("Bacteria", "Fungi", "Archaea"),
  Mantel_r = c(bacteria_mantel$statistic, fungi_mantel$statistic, archaea_mantel$statistic),
  Mantel_p = c(bacteria_mantel$signif, fungi_mantel$signif, archaea_mantel$signif),
  Significant = c("No", "No", "No")
)

# Create summary plot
p5 <- ggplot(summary_data, aes(x = Group, y = Mantel_r, fill = Significant)) +
  geom_col(alpha = 0.7, color = "black") +
  geom_hline(yintercept = 0, linetype = "dashed", color = "black") +
  geom_text(aes(label = paste("r =", round(Mantel_r, 3), "\np =", round(Mantel_p, 3))), 
            vjust = ifelse(summary_data$Mantel_r >= 0, -0.5, 1.5), size = 3.5, fontface = "bold") +
  scale_fill_manual(values = c("No" = "lightcoral")) +
  labs(
    title = "No Significant Environmental Correlations",
    subtitle = "All p-values > 0.05 (not significant)",
    x = "Microbial Group",
    y = "Mantel Correlation (r)",
    fill = "Significant?"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12, color = "red"),
    legend.position = "bottom",
    panel.grid.minor = element_blank()
  ) +
  ylim(-0.6, 0.2)

# =============================================================================
# PLOT 4: Environmental Homogeneity Demonstration
# =============================================================================

# Show pH variation (most important variable)
ph_data <- data.frame(
  Site = env_data$Site,
  pH = env_data$pH
)

p6 <- ggplot(ph_data, aes(x = Site, y = pH)) +
  geom_point(size = 4, color = "darkblue") +
  geom_hline(yintercept = mean(ph_data$pH), linetype = "dashed", color = "red") +
  geom_text(aes(label = round(pH, 2)), vjust = -1, size = 3, fontface = "bold") +
  labs(
    title = "Environmental Homogeneity: pH Example",
    subtitle = paste("Range:", round(max(ph_data$pH) - min(ph_data$pH), 2), 
                    "units (very narrow)"),
    x = "Site",
    y = "pH"
  ) +
  theme_minimal() +
  theme(
    plot.title = element_text(size = 14, face = "bold"),
    plot.subtitle = element_text(size = 12),
    panel.grid.minor = element_blank()
  ) +
  annotate("text", x = Inf, y = Inf, 
           label = "Sites are\nenvironmentally\nsimilar", 
           hjust = 1.1, vjust = 1.1, 
           size = 4, color = "red", fontface = "bold")

# =============================================================================
# COMBINE AND SAVE PLOTS
# =============================================================================

# Create comprehensive figure
combined_plot <- grid.arrange(
  p1, p2, p3,  # Mantel plots
  p4, p5, p6,  # Supporting plots
  ncol = 3, nrow = 2,
  top = "Demonstration: No Environmental Correlation with Microbial Communities"
)

# Save comprehensive plot
ggsave("no_correlation_comprehensive.png", combined_plot, 
       width = 18, height = 12, dpi = 300)

# Save individual Mantel plots
mantel_combined <- grid.arrange(p1, p2, p3, ncol = 3,
                               top = "Mantel Tests: No Significant Environmental Correlations")
ggsave("mantel_no_correlation.png", mantel_combined, 
       width = 18, height = 6, dpi = 300)

# Save summary plot
ggsave("correlation_summary.png", p5, width = 8, height = 6, dpi = 300)

cat("=== NO CORRELATION PLOTS CREATED ===\n")
cat("Files saved:\n")
cat("- no_correlation_comprehensive.png (6-panel comprehensive figure)\n")
cat("- mantel_no_correlation.png (3-panel Mantel test figure)\n") 
cat("- correlation_summary.png (summary statistics)\n")

cat("\n=== INTERPRETATION ===\n")
cat("These plots clearly demonstrate:\n")
cat("1. No significant correlations between environment and communities\n")
cat("2. Limited environmental variation among sites\n")
cat("3. Weak Mantel correlations (all p > 0.05)\n")
cat("4. Environmental homogeneity explains lack of correlation\n")

cat("\n🎯 NO CORRELATION DEMONSTRATION COMPLETE! 🎯\n")
