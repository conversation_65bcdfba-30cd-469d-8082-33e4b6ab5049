# DESeq2 Analysis for Differential Abundance
# Comparing genera between Substrate types and Sites

# Load required libraries
library(readxl)
library(DESeq2)
library(dplyr)
library(ggplot2)
library(pheatmap)
library(RColorBrewer)
library(ggrepel)
library(tidyr)

# Function to prepare data for DESeq2 analysis
prepare_deseq_data <- function(group_name) {
  cat("=== PREPARING", toupper(group_name), "DATA ===\n")
  
  # Read data
  otu_file <- paste0("OTU_table_", group_name, ".xlsx")
  metadata_file <- paste0("metadata_", group_name, ".xlsx")
  taxonomy_file <- paste0("taxonomy_", group_name, ".xlsx")
  
  otu_table <- read_excel(otu_file)
  metadata <- read_excel(metadata_file)
  taxonomy <- read_excel(taxonomy_file)
  
  # Prepare OTU count matrix (samples as columns, OTUs as rows)
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Ensure sample names match between metadata and count matrix
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Convert counts to integers (required for DESeq2)
  count_matrix <- round(count_matrix)
  mode(count_matrix) <- "integer"
  
  # Add site information (extract from Site column)
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  
  cat("Data dimensions:\n")
  cat("- Count matrix:", dim(count_matrix), "\n")
  cat("- Metadata:", dim(metadata), "\n")
  cat("- Taxonomy:", dim(taxonomy), "\n")
  cat("- Substrates:", table(metadata$Substrate), "\n")
  cat("- Sites:", table(metadata$SiteNumber), "\n")
  
  return(list(
    counts = count_matrix,
    metadata = metadata,
    taxonomy = taxonomy
  ))
}

# Function to aggregate counts to genus level
aggregate_to_genus <- function(count_matrix, taxonomy) {
  cat("Aggregating to genus level...\n")
  
  # Match OTUs between count matrix and taxonomy
  common_otus <- intersect(rownames(count_matrix), taxonomy[[1]])
  count_matrix <- count_matrix[common_otus, ]
  taxonomy <- taxonomy[taxonomy[[1]] %in% common_otus, ]
  taxonomy <- taxonomy[match(rownames(count_matrix), taxonomy[[1]]), ]
  
  # Get genus information (assuming it's in column 6 or 7)
  genus_col <- which(grepl("genus|Genus", colnames(taxonomy), ignore.case = TRUE))
  if (length(genus_col) == 0) {
    # Try the last column or 6th column
    genus_col <- min(6, ncol(taxonomy))
  }
  
  genus_info <- taxonomy[[genus_col]]
  
  # Clean genus names
  genus_info <- gsub("g__", "", genus_info)
  genus_info <- gsub("^$", "Unknown", genus_info)
  genus_info <- gsub("^NA$", "Unknown", genus_info)
  genus_info[is.na(genus_info)] <- "Unknown"
  
  # Aggregate counts by genus
  genus_counts <- aggregate(count_matrix, by = list(Genus = genus_info), FUN = sum)
  rownames(genus_counts) <- genus_counts$Genus
  genus_counts <- genus_counts[, -1]
  
  cat("Aggregated to", nrow(genus_counts), "genera\n")
  cat("Top 10 most abundant genera:\n")
  genus_totals <- rowSums(genus_counts)
  print(head(sort(genus_totals, decreasing = TRUE), 10))
  
  return(genus_counts)
}

# Function to run DESeq2 analysis
run_deseq_analysis <- function(count_matrix, metadata, comparison_type = "substrate") {
  cat("\n=== RUNNING DESEQ2 ANALYSIS ===\n")
  cat("Comparison type:", comparison_type, "\n")
  
  # Filter low abundance genera (present in at least 10% of samples with >10 reads)
  min_samples <- ceiling(0.1 * ncol(count_matrix))
  keep <- rowSums(count_matrix >= 10) >= min_samples
  count_matrix <- count_matrix[keep, ]
  
  cat("After filtering:", nrow(count_matrix), "genera retained\n")
  
  # Create DESeq2 dataset
  if (comparison_type == "substrate") {
    # Compare Substrate types
    dds <- DESeqDataSetFromMatrix(
      countData = count_matrix,
      colData = metadata,
      design = ~ Substrate
    )
    contrast_var <- "Substrate"
    contrast_levels <- c("Biofilm", "Sediment")  # Biofilm vs Sediment
    
  } else if (comparison_type == "site") {
    # Compare Sites (only for samples that have both substrates)
    sites_with_both <- metadata %>%
      group_by(SiteNumber) %>%
      summarise(n_substrates = n_distinct(Substrate)) %>%
      filter(n_substrates == 2) %>%
      pull(SiteNumber)
    
    if (length(sites_with_both) > 0) {
      metadata_subset <- metadata[metadata$SiteNumber %in% sites_with_both, ]
      count_subset <- count_matrix[, metadata_subset$Sample]
      
      dds <- DESeqDataSetFromMatrix(
        countData = count_subset,
        colData = metadata_subset,
        design = ~ SiteNumber + Substrate
      )
      contrast_var <- "Substrate"
      contrast_levels <- c("Biofilm", "Sediment")
    } else {
      cat("No sites with both substrates found for comparison\n")
      return(NULL)
    }
  }
  
  # Run DESeq2
  dds <- DESeq(dds)
  
  # Get results
  res <- results(dds, contrast = c(contrast_var, contrast_levels[1], contrast_levels[2]))
  res <- res[order(res$padj), ]
  
  # Summary
  cat("\nDESeq2 Results Summary:\n")
  print(summary(res))
  
  # Significant results
  sig_res <- res[!is.na(res$padj) & res$padj < 0.05, ]
  cat("\nSignificant genera (padj < 0.05):", nrow(sig_res), "\n")
  
  if (nrow(sig_res) > 0) {
    cat("\nTop 20 most significant genera:\n")
    print(head(sig_res, 20))
  }
  
  return(list(
    dds = dds,
    results = res,
    significant = sig_res,
    comparison = paste(contrast_levels[1], "vs", contrast_levels[2])
  ))
}

# Function to create visualizations
create_deseq_plots <- function(deseq_results, group_name, comparison_type) {
  res <- deseq_results$results
  sig_res <- deseq_results$significant
  
  # 1. Volcano plot
  volcano_data <- data.frame(
    Genus = rownames(res),
    log2FC = res$log2FoldChange,
    padj = res$padj,
    significant = !is.na(res$padj) & res$padj < 0.05
  )
  
  # Remove infinite values
  volcano_data <- volcano_data[is.finite(volcano_data$log2FC), ]
  
  p1 <- ggplot(volcano_data, aes(x = log2FC, y = -log10(padj))) +
    geom_point(aes(color = significant), alpha = 0.6) +
    scale_color_manual(values = c("FALSE" = "gray", "TRUE" = "red")) +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "blue") +
    geom_vline(xintercept = 0, linetype = "dashed", color = "gray") +
    labs(
      title = paste("Volcano Plot -", group_name, "\n", deseq_results$comparison),
      x = "Log2 Fold Change",
      y = "-Log10 Adjusted P-value"
    ) +
    theme_minimal() +
    theme(legend.position = "none")
  
  # Add labels for top significant genera
  if (nrow(sig_res) > 0) {
    top_sig <- head(sig_res, 10)
    top_sig_data <- volcano_data[volcano_data$Genus %in% rownames(top_sig), ]
    
    p1 <- p1 + geom_text_repel(
      data = top_sig_data,
      aes(label = Genus),
      size = 3,
      max.overlaps = 10
    )
  }
  
  # 2. MA plot
  p2 <- ggplot(volcano_data, aes(x = log10(res$baseMean + 1), y = log2FC)) +
    geom_point(aes(color = significant), alpha = 0.6) +
    scale_color_manual(values = c("FALSE" = "gray", "TRUE" = "red")) +
    geom_hline(yintercept = 0, linetype = "dashed", color = "gray") +
    labs(
      title = paste("MA Plot -", group_name, "\n", deseq_results$comparison),
      x = "Log10 Mean Expression",
      y = "Log2 Fold Change"
    ) +
    theme_minimal() +
    theme(legend.position = "none")
  
  return(list(volcano = p1, ma = p2))
}

# Main analysis function
analyze_group <- function(group_name) {
  cat("\n", "="*50, "\n")
  cat("ANALYZING", toupper(group_name), "\n")
  cat("="*50, "\n")
  
  # Prepare data
  data <- prepare_deseq_data(group_name)
  
  # Aggregate to genus level
  genus_counts <- aggregate_to_genus(data$counts, data$taxonomy)
  
  # Run DESeq2 analysis for substrate comparison
  cat("\n--- SUBSTRATE COMPARISON ---\n")
  substrate_results <- run_deseq_analysis(genus_counts, data$metadata, "substrate")
  
  # Create plots
  if (!is.null(substrate_results)) {
    plots <- create_deseq_plots(substrate_results, group_name, "substrate")
    
    # Save plots
    ggsave(paste0("volcano_plot_", group_name, "_substrate.png"), 
           plots$volcano, width = 10, height = 8, dpi = 300)
    ggsave(paste0("ma_plot_", group_name, "_substrate.png"), 
           plots$ma, width = 10, height = 8, dpi = 300)
    
    # Save results
    write.csv(substrate_results$results, 
              paste0("deseq_results_", group_name, "_substrate.csv"))
    write.csv(substrate_results$significant, 
              paste0("deseq_significant_", group_name, "_substrate.csv"))
  }
  
  return(list(
    data = data,
    genus_counts = genus_counts,
    substrate_results = substrate_results
  ))
}

# Run analysis
cat("DIFFERENTIAL ABUNDANCE ANALYSIS WITH DESEQ2\n")
cat("===========================================\n")

# Analyze bacteria first (most comprehensive dataset)
bacteria_analysis <- analyze_group("bacteria")

cat("\n=== ANALYSIS COMPLETE ===\n")
cat("Results saved to CSV files and plots saved as PNG files\n")
