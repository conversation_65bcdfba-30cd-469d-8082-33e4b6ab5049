# Differential Abundance Analysis using Wilcoxon Tests
# Alternative to DESeq2 for identifying differentially abundant genera

library(readxl)
library(dplyr)
library(ggplot2)
library(tidyr)
library(ggrepel)

# Function to prepare and aggregate data to genus level
prepare_genus_data <- function(group_name) {
  cat("=== PREPARING", toupper(group_name), "DATA ===\n")
  
  # Read data
  otu_file <- paste0("OTU_table_", group_name, ".xlsx")
  metadata_file <- paste0("metadata_", group_name, ".xlsx")
  taxonomy_file <- paste0("taxonomy_", group_name, ".xlsx")
  
  otu_table <- read_excel(otu_file)
  metadata <- read_excel(metadata_file)
  taxonomy <- read_excel(taxonomy_file)
  
  # Prepare count matrix
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Add site number
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  
  # Match OTUs with taxonomy
  common_otus <- intersect(rownames(count_matrix), taxonomy[[1]])
  count_matrix <- count_matrix[common_otus, ]
  taxonomy <- taxonomy[taxonomy[[1]] %in% common_otus, ]
  taxonomy <- taxonomy[match(rownames(count_matrix), taxonomy[[1]]), ]
  
  # Find genus column
  genus_col <- which(grepl("genus|Genus", colnames(taxonomy), ignore.case = TRUE))
  if (length(genus_col) == 0) {
    genus_col <- min(6, ncol(taxonomy))
  }
  
  genus_info <- taxonomy[[genus_col]]
  genus_info <- gsub("g__", "", genus_info)
  genus_info <- gsub("^$|^NA$", "Unknown", genus_info)
  genus_info[is.na(genus_info)] <- "Unknown"
  
  # Aggregate to genus level
  genus_counts <- aggregate(count_matrix, by = list(Genus = genus_info), FUN = sum)
  rownames(genus_counts) <- genus_counts$Genus
  genus_counts <- genus_counts[, -1]
  
  # Convert to relative abundance
  genus_rel <- sweep(genus_counts, 2, colSums(genus_counts), "/")
  
  cat("Data summary:\n")
  cat("- Samples:", ncol(genus_counts), "\n")
  cat("- Genera:", nrow(genus_counts), "\n")
  cat("- Substrates:", table(metadata$Substrate), "\n")
  cat("- Sites:", table(metadata$SiteNumber), "\n")
  
  return(list(
    counts = genus_counts,
    rel_abundance = genus_rel,
    metadata = metadata,
    taxonomy = taxonomy
  ))
}

# Function to perform differential abundance testing
test_differential_abundance <- function(genus_rel, metadata, min_prevalence = 0.1, min_abundance = 0.001) {
  cat("\n=== DIFFERENTIAL ABUNDANCE TESTING ===\n")
  
  # Filter genera (present in at least 10% of samples with >0.1% relative abundance)
  min_samples <- ceiling(min_prevalence * ncol(genus_rel))
  keep <- rowSums(genus_rel >= min_abundance) >= min_samples
  genus_rel_filt <- genus_rel[keep, ]
  
  cat("After filtering:", nrow(genus_rel_filt), "genera retained\n")
  
  # Prepare results dataframe
  results <- data.frame(
    Genus = rownames(genus_rel_filt),
    Mean_Biofilm = NA,
    Mean_Sediment = NA,
    Log2FC = NA,
    P_value = NA,
    P_adjusted = NA,
    Significant = FALSE,
    stringsAsFactors = FALSE
  )
  
  # Get substrate groups
  biofilm_samples <- metadata$Sample[metadata$Substrate == "Biofilm"]
  sediment_samples <- metadata$Sample[metadata$Substrate == "Sediment"]
  
  cat("Testing", nrow(genus_rel_filt), "genera...\n")
  
  # Perform tests for each genus
  for (i in 1:nrow(genus_rel_filt)) {
    genus_name <- rownames(genus_rel_filt)[i]

    # Get abundance data and convert to numeric
    biofilm_abund <- as.numeric(genus_rel_filt[i, biofilm_samples])
    sediment_abund <- as.numeric(genus_rel_filt[i, sediment_samples])

    # Calculate means
    mean_biofilm <- mean(biofilm_abund, na.rm = TRUE)
    mean_sediment <- mean(sediment_abund, na.rm = TRUE)
    
    # Calculate log2 fold change (add pseudocount to avoid log(0))
    log2fc <- log2((mean_biofilm + 1e-6) / (mean_sediment + 1e-6))
    
    # Perform Wilcoxon test
    if (length(biofilm_abund) > 1 && length(sediment_abund) > 1) {
      test_result <- wilcox.test(biofilm_abund, sediment_abund)
      p_value <- test_result$p.value
    } else {
      p_value <- NA
    }
    
    # Store results
    results[i, "Mean_Biofilm"] <- mean_biofilm
    results[i, "Mean_Sediment"] <- mean_sediment
    results[i, "Log2FC"] <- log2fc
    results[i, "P_value"] <- p_value
  }
  
  # Adjust p-values
  results$P_adjusted <- p.adjust(results$P_value, method = "BH")
  results$Significant <- !is.na(results$P_adjusted) & results$P_adjusted < 0.05
  
  # Sort by adjusted p-value
  results <- results[order(results$P_adjusted, na.last = TRUE), ]
  
  # Summary
  n_significant <- sum(results$Significant, na.rm = TRUE)
  n_enriched_biofilm <- sum(results$Significant & results$Log2FC > 0, na.rm = TRUE)
  n_enriched_sediment <- sum(results$Significant & results$Log2FC < 0, na.rm = TRUE)
  
  cat("\nResults Summary:\n")
  cat("- Total genera tested:", nrow(results), "\n")
  cat("- Significant genera (padj < 0.05):", n_significant, "\n")
  cat("- Enriched in Biofilm:", n_enriched_biofilm, "\n")
  cat("- Enriched in Sediment:", n_enriched_sediment, "\n")
  
  if (n_significant > 0) {
    cat("\nTop 20 most significant genera:\n")
    print(head(results[results$Significant, ], 20))
  }
  
  return(results)
}

# Function to create visualizations
create_plots <- function(results, group_name) {
  cat("\n=== CREATING VISUALIZATIONS ===\n")
  
  # Remove infinite values for plotting
  plot_data <- results[is.finite(results$Log2FC) & !is.na(results$P_adjusted), ]
  
  # 1. Volcano plot
  p1 <- ggplot(plot_data, aes(x = Log2FC, y = -log10(P_adjusted))) +
    geom_point(aes(color = Significant), alpha = 0.6, size = 2) +
    scale_color_manual(values = c("FALSE" = "gray60", "TRUE" = "red")) +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "blue") +
    geom_vline(xintercept = 0, linetype = "dashed", color = "gray30") +
    labs(
      title = paste("Differential Abundance Analysis -", toupper(group_name)),
      subtitle = "Biofilm vs Sediment (Wilcoxon Test)",
      x = "Log2 Fold Change (Biofilm/Sediment)",
      y = "-Log10 Adjusted P-value"
    ) +
    theme_minimal() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12)
    )
  
  # Add labels for significant genera
  sig_data <- plot_data[plot_data$Significant, ]
  if (nrow(sig_data) > 0) {
    # Label top 15 most significant
    top_sig <- head(sig_data, 15)
    p1 <- p1 + geom_text_repel(
      data = top_sig,
      aes(label = Genus),
      size = 3,
      max.overlaps = 15,
      box.padding = 0.3,
      point.padding = 0.3
    )
  }
  
  # 2. Bar plot of top differentially abundant genera
  if (nrow(sig_data) > 0) {
    top_genera <- head(sig_data, 20)
    top_genera$Direction <- ifelse(top_genera$Log2FC > 0, "Biofilm", "Sediment")
    top_genera$Genus <- factor(top_genera$Genus, levels = rev(top_genera$Genus))
    
    p2 <- ggplot(top_genera, aes(x = Genus, y = Log2FC, fill = Direction)) +
      geom_col() +
      scale_fill_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
      coord_flip() +
      labs(
        title = paste("Top 20 Differentially Abundant Genera -", toupper(group_name)),
        x = "Genus",
        y = "Log2 Fold Change (Biofilm/Sediment)",
        fill = "Enriched in"
      ) +
      theme_minimal() +
      theme(
        plot.title = element_text(size = 14, face = "bold"),
        axis.text.y = element_text(size = 10)
      )
  } else {
    p2 <- ggplot() + 
      labs(title = paste("No significant genera found -", toupper(group_name))) +
      theme_minimal()
  }
  
  return(list(volcano = p1, barplot = p2))
}

# Function to analyze site effects
analyze_site_effects <- function(genus_rel, metadata) {
  cat("\n=== SITE EFFECTS ANALYSIS ===\n")
  
  # Only analyze sites that have both substrates
  sites_with_both <- metadata %>%
    group_by(SiteNumber) %>%
    summarise(n_substrates = n_distinct(Substrate)) %>%
    filter(n_substrates == 2) %>%
    pull(SiteNumber)
  
  cat("Sites with both substrates:", paste(sites_with_both, collapse = ", "), "\n")
  
  if (length(sites_with_both) == 0) {
    cat("No sites with both substrates found\n")
    return(NULL)
  }
  
  # Filter data for these sites
  site_metadata <- metadata[metadata$SiteNumber %in% sites_with_both, ]
  site_genus <- genus_rel[, site_metadata$Sample]
  
  cat("Samples for site analysis:", ncol(site_genus), "\n")
  
  # Create site comparison summary
  site_summary <- site_metadata %>%
    group_by(SiteNumber, Substrate) %>%
    summarise(n_samples = n(), .groups = "drop")
  
  print(site_summary)
  
  return(list(
    genus_data = site_genus,
    metadata = site_metadata,
    summary = site_summary
  ))
}

# Main analysis function
main_analysis <- function(group_name) {
  cat("\n", paste(rep("=", 60), collapse = ""), "\n")
  cat("DIFFERENTIAL ABUNDANCE ANALYSIS:", toupper(group_name), "\n")
  cat(paste(rep("=", 60), collapse = ""), "\n")
  
  # Prepare data
  data <- prepare_genus_data(group_name)
  
  # Test differential abundance
  results <- test_differential_abundance(data$rel_abundance, data$metadata)
  
  # Create plots
  plots <- create_plots(results, group_name)
  
  # Save results
  write.csv(results, paste0("differential_abundance_", group_name, ".csv"), row.names = FALSE)
  
  # Save plots
  ggsave(paste0("volcano_plot_", group_name, ".png"), plots$volcano, 
         width = 12, height = 8, dpi = 300)
  ggsave(paste0("barplot_", group_name, ".png"), plots$barplot, 
         width = 12, height = 8, dpi = 300)
  
  # Analyze site effects
  site_analysis <- analyze_site_effects(data$rel_abundance, data$metadata)
  
  cat("\nFiles saved:\n")
  cat("- Results:", paste0("differential_abundance_", group_name, ".csv"), "\n")
  cat("- Volcano plot:", paste0("volcano_plot_", group_name, ".png"), "\n")
  cat("- Bar plot:", paste0("barplot_", group_name, ".png"), "\n")
  
  return(list(
    data = data,
    results = results,
    plots = plots,
    site_analysis = site_analysis
  ))
}

# Run analysis
cat("DIFFERENTIAL ABUNDANCE ANALYSIS\n")
cat("===============================\n")
cat("Using Wilcoxon tests to identify genera that differ between substrates\n\n")

# Analyze bacteria
bacteria_results <- main_analysis("bacteria")

# Analyze fungi and archaea
fungi_results <- main_analysis("fungi")
archaea_results <- main_analysis("archaea")

cat("\n", paste(rep("=", 60), collapse = ""), "\n")
cat("ANALYSIS COMPLETE\n")
cat(paste(rep("=", 60), collapse = ""), "\n")
cat("All results and visualizations have been saved to files.\n")
