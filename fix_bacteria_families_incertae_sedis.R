# Fix Bacterial Families Plot - Move Incertae sedis to Others
# Show top 20 families excluding Incertae sedis

library(readxl)
library(ggplot2)
library(dplyr)
library(tidyr)
library(RColorBrewer)
library(gridExtra)
library(scales)

# Function to create bacterial families barplot with Incertae sedis moved to Others
create_bacteria_families_fixed <- function() {
  group_name <- "bacteria"
  cat("=== FIXING BACTERIAL FAMILIES (MOVING INCERTAE SEDIS TO OTHERS) ===\n")
  
  # Read data
  otu_table <- read_excel(paste0("OTU_table_", group_name, ".xlsx"))
  metadata <- read_excel(paste0("metadata_", group_name, ".xlsx"))
  taxonomy <- read_excel(paste0("taxonomy_", group_name, ".xlsx"))
  
  # Prepare matrices
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples
  common_samples <- intersect(colnames(count_matrix), metadata$<PERSON>ple)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Match taxonomy
  common_otus <- intersect(rownames(count_matrix), taxonomy[[1]])
  count_matrix <- count_matrix[common_otus, ]
  taxonomy <- taxonomy[taxonomy[[1]] %in% common_otus, ]
  taxonomy <- taxonomy[match(rownames(count_matrix), taxonomy[[1]]), ]
  
  cat("Data dimensions:", nrow(count_matrix), "OTUs x", ncol(count_matrix), "samples\n")
  
  # Create site labels
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  metadata$SiteLabel <- ifelse(
    metadata$Substrate == "Biofilm",
    paste0("BF", metadata$SiteNumber),
    paste0("SED", metadata$SiteNumber)
  )
  
  # Convert to relative abundance
  rel_abundance <- sweep(count_matrix, 2, colSums(count_matrix), "/") * 100
  
  # Function to create barplot data for families with Incertae sedis handling
  create_families_data_fixed <- function() {
    cat("Processing Family level with Incertae sedis moved to Others...\n")
    
    # Get family taxonomy column
    tax_col <- "Family"
    
    # Handle missing taxonomy
    tax_vector <- taxonomy[[tax_col]]
    tax_vector[is.na(tax_vector) | tax_vector == "" | tax_vector == "unidentified"] <- "Unassigned"
    
    # Check for Incertae sedis variants (including the actual format found)
    incertae_patterns <- c("Incertae Sedis", "Incertae sedis", "incertae sedis", "Incertae_sedis", "incertae_sedis")
    incertae_indices <- which(tax_vector %in% incertae_patterns)
    
    if (length(incertae_indices) > 0) {
      cat("Found", length(incertae_indices), "OTUs with Incertae sedis classification\n")
      cat("Incertae sedis variants found:", unique(tax_vector[incertae_indices]), "\n")
    }
    
    # Aggregate by taxonomy
    tax_abundance <- data.frame(
      Taxonomy = tax_vector,
      rel_abundance,
      stringsAsFactors = FALSE
    )
    
    # Sum by taxonomy
    tax_summary <- tax_abundance %>%
      group_by(Taxonomy) %>%
      summarise(across(everything(), sum), .groups = 'drop')
    
    # Convert back to matrix
    tax_matrix <- as.matrix(tax_summary[, -1])
    rownames(tax_matrix) <- tax_summary$Taxonomy
    
    # Ensure each sample column sums to exactly 100%
    for (j in 1:ncol(tax_matrix)) {
      col_sum <- sum(tax_matrix[, j])
      if (abs(col_sum - 100) > 0.001) {
        tax_matrix[, j] <- (tax_matrix[, j] / col_sum) * 100
      }
    }
    
    # Aggregate by site (mean of replicates)
    site_abundance <- data.frame()
    
    for (site in unique(metadata$SiteLabel)) {
      site_samples <- metadata$Sample[metadata$SiteLabel == site]
      site_indices <- match(site_samples, colnames(tax_matrix))
      
      if (length(site_indices) > 1) {
        site_mean <- rowMeans(tax_matrix[, site_indices, drop = FALSE])
      } else {
        site_mean <- tax_matrix[, site_indices]
      }
      
      # Ensure site sums to 100%
      site_total <- sum(site_mean)
      if (abs(site_total - 100) > 0.001) {
        site_mean <- (site_mean / site_total) * 100
      }
      
      site_data <- data.frame(
        Site = site,
        Substrate = metadata$Substrate[metadata$SiteLabel == site][1],
        Taxonomy = names(site_mean),
        Abundance = as.numeric(site_mean),
        stringsAsFactors = FALSE
      )
      
      site_abundance <- rbind(site_abundance, site_data)
    }
    
    # Calculate total abundance per taxonomy across all sites
    tax_totals <- site_abundance %>%
      group_by(Taxonomy) %>%
      summarise(Total = sum(Abundance), .groups = 'drop') %>%
      arrange(desc(Total))
    
    cat("Total families before filtering:", nrow(tax_totals), "\n")
    
    # Show Incertae sedis abundance if present
    incertae_total <- tax_totals %>% 
      filter(Taxonomy %in% incertae_patterns) %>% 
      summarise(Total = sum(Total))
    
    if (nrow(incertae_total) > 0 && incertae_total$Total > 0) {
      cat("Incertae sedis total abundance:", round(incertae_total$Total, 2), "%\n")
      cat("Incertae sedis rank:", which(tax_totals$Taxonomy %in% incertae_patterns), "\n")
    }
    
    # Get top 20 families EXCLUDING Incertae sedis and Unassigned
    exclude_taxa <- c(incertae_patterns, "Unassigned")
    top_families <- tax_totals %>%
      filter(!Taxonomy %in% exclude_taxa) %>%
      head(20) %>%
      pull(Taxonomy)
    
    cat("Top 20 families (excluding Incertae sedis and Unassigned):\n")
    for (i in 1:length(top_families)) {
      family_total <- tax_totals$Total[tax_totals$Taxonomy == top_families[i]]
      cat(i, ".", top_families[i], "-", round(family_total, 2), "%\n")
    }
    
    # Create grouped taxonomy: Top 20 + Others (including Incertae sedis) + Unassigned
    site_abundance_final <- site_abundance %>%
      mutate(
        Taxonomy_grouped = case_when(
          Taxonomy == "Unassigned" ~ "Unassigned",
          Taxonomy %in% top_families ~ Taxonomy,
          TRUE ~ "Others"  # This includes Incertae sedis
        )
      ) %>%
      group_by(Site, Substrate, Taxonomy_grouped) %>%
      summarise(Abundance = sum(Abundance), .groups = 'drop') %>%
      rename(Taxonomy = Taxonomy_grouped)
    
    # Check how much went into Others
    others_total <- site_abundance_final %>%
      filter(Taxonomy == "Others") %>%
      summarise(Total = sum(Abundance))
    
    cat("Total abundance moved to Others (including Incertae sedis):", round(others_total$Total, 2), "%\n")
    
    # Ensure all sites have all taxonomy categories
    all_sites <- unique(site_abundance_final$Site)
    all_taxa <- unique(site_abundance_final$Taxonomy)
    
    complete_data <- expand.grid(
      Site = all_sites,
      Taxonomy = all_taxa,
      stringsAsFactors = FALSE
    ) %>%
      left_join(
        site_abundance_final %>% select(Site, Substrate) %>% distinct(),
        by = "Site"
      ) %>%
      left_join(site_abundance_final, by = c("Site", "Taxonomy", "Substrate")) %>%
      mutate(Abundance = ifelse(is.na(Abundance), 0, Abundance))
    
    # Apply normalization to ensure 100%
    complete_data <- complete_data %>%
      group_by(Site) %>%
      mutate(
        Total = sum(Abundance, na.rm = TRUE),
        Abundance = ifelse(Total > 0, (Abundance / Total) * 100, Abundance)
      ) %>%
      ungroup() %>%
      select(-Total)
    
    # Final adjustment for exact 100%
    complete_data <- complete_data %>%
      group_by(Site) %>%
      mutate(
        Site_Total = sum(Abundance, na.rm = TRUE),
        Difference = 100 - Site_Total,
        Max_Index = row_number() == which.max(Abundance),
        Abundance = ifelse(Max_Index & abs(Difference) > 0.0001, 
                          Abundance + Difference, 
                          Abundance)
      ) %>%
      ungroup() %>%
      select(-Site_Total, -Difference, -Max_Index)
    
    # Order sites properly
    complete_data$Site <- factor(complete_data$Site, 
                                levels = c(paste0("SED", 1:7), paste0("BF", 1:7)))
    
    # Order taxonomy for plotting (top families first, then Others, then Unassigned)
    tax_order <- c(top_families, "Others", "Unassigned")
    complete_data$Taxonomy <- factor(complete_data$Taxonomy, levels = rev(tax_order))
    
    # Verify totals
    site_totals <- complete_data %>%
      group_by(Site) %>%
      summarise(Total = sum(Abundance, na.rm = TRUE), .groups = 'drop')
    
    cat("Site totals verification (Families - Incertae sedis moved to Others):\n")
    for (i in 1:nrow(site_totals)) {
      total <- round(site_totals$Total[i], 2)
      cat("✓ Site", site_totals$Site[i], "sums to", total, "%\n")
    }
    
    return(list(
      data = complete_data,
      n_taxa = length(tax_order),
      tax_level = "Family"
    ))
  }
  
  # Create the fixed families data
  family_data <- create_families_data_fixed()
  
  # Function to create the plot
  create_plot <- function(plot_data) {
    # Create color palette
    n_colors <- plot_data$n_taxa
    if (n_colors <= 12) {
      colors <- brewer.pal(max(3, n_colors), "Set3")
    } else {
      colors <- c(brewer.pal(12, "Set3"), 
                  brewer.pal(min(8, n_colors - 12), "Dark2"))
    }
    
    # Ensure we have enough colors
    if (length(colors) < n_colors) {
      colors <- rep(colors, length.out = n_colors)
    }
    
    # Assign specific colors to special categories
    tax_levels <- levels(plot_data$data$Taxonomy)
    color_mapping <- setNames(colors[1:length(tax_levels)], tax_levels)
    
    # Set specific colors for special categories
    if ("Unassigned" %in% tax_levels) {
      color_mapping["Unassigned"] <- "gray80"
    }
    if ("Others" %in% tax_levels) {
      color_mapping["Others"] <- "gray60"
    }
    
    # Create the plot
    p <- ggplot(plot_data$data, aes(x = Site, y = Abundance, fill = Taxonomy)) +
      geom_bar(stat = "identity", color = "white", linewidth = 0.2) +
      scale_fill_manual(values = color_mapping) +
      scale_y_continuous(labels = function(x) paste0(x, "%"), 
                        limits = c(0, 100),
                        expand = c(0, 0)) +
      labs(
        title = "Top 20 Family Composition - BACTERIA (Incertae sedis moved to Others)",
        subtitle = "Abundance as percentage (mean of replicates per site)",
        x = "Site",
        y = "Relative Abundance (%)",
        fill = "Family"
      ) +
      theme_minimal() +
      theme(
        plot.title = element_text(size = 14, face = "bold"),
        plot.subtitle = element_text(size = 12),
        axis.text.x = element_text(angle = 45, hjust = 1, size = 10),
        axis.text.y = element_text(size = 10),
        axis.title = element_text(size = 12),
        legend.title = element_text(size = 12, face = "bold"),
        legend.text = element_text(size = 10),
        legend.position = "right",
        panel.grid.major.x = element_blank(),
        panel.grid.minor = element_blank()
      ) +
      guides(fill = guide_legend(ncol = 1, reverse = TRUE))
    
    # Add substrate labels
    p <- p + 
      annotate("text", x = 4, y = 105, label = "SEDIMENT", 
               size = 4, fontface = "bold", color = "darkblue") +
      annotate("text", x = 11, y = 105, label = "BIOFILM", 
               size = 4, fontface = "bold", color = "darkred")
    
    return(p)
  }
  
  # Create the plot
  family_plot <- create_plot(family_data)
  
  # Save the fixed plot
  ggsave("taxonomic_barplot_family_bacteria_FIXED_no_incertae.pdf", 
         family_plot, width = 14, height = 10, device = "pdf")
  
  cat("\n=== BACTERIAL FAMILIES FIXED FILE SAVED ===\n")
  cat("- taxonomic_barplot_family_bacteria_FIXED_no_incertae.pdf\n")
  cat("- Incertae sedis moved to Others category\n")
  cat("- Top 20 most abundant families (excluding Incertae sedis) shown\n")
  
  return(family_plot)
}

# Main execution
cat("BACTERIAL FAMILIES FIX - MOVE INCERTAE SEDIS TO OTHERS\n")
cat("=====================================================\n")
cat("This will modify only the bacterial families plot\n")
cat("- Move Incertae sedis to Others category\n")
cat("- Show top 20 families excluding Incertae sedis\n\n")

# Run the fix
bacteria_families_fixed <- create_bacteria_families_fixed()

cat("\n🎯 BACTERIAL FAMILIES PLOT FIXED! 🎯\n")
cat("Incertae sedis moved to Others, top 20 families displayed.\n")
