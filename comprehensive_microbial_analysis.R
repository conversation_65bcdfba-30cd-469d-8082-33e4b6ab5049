# Load required libraries
library(readxl)
library(vegan)
library(ggplot2)
library(dplyr)
library(tidyr)
library(gridExtra)
library(RColorBrewer)
library(ape)
library(grid)
library(ggrepel)


# Function to prepare data with corrected site labels
prepare_data <- function(group_name) {
  cat("=== PREPARING", toupper(group_name), "DATA ===\n")
  
  # Read data files
  otu_file <- paste0("OTU_table_", group_name, ".xlsx")
  metadata_file <- paste0("metadata_", group_name, ".xlsx")
  taxonomy_file <- paste0("taxonomy_", group_name, ".xlsx")
  
  otu_table <- read_excel(otu_file)
  metadata <- read_excel(metadata_file)
  taxonomy <- read_excel(taxonomy_file)
  
  # Prepare count matrix
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples between OTU table and metadata
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Create corrected site labels (biofilm and sediment sites are independent)
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  metadata$SiteLabel <- ifelse(
    metadata$Substrate == "Biofilm",
    paste0("BF", metadata$SiteNumber),  # BF1, BF2 for biofilm sites
    paste0("SED", metadata$SiteNumber)  # SED1-SED7 for sediment sites
  )
  
  # Print site distribution summary
  site_summary <- metadata %>%
    group_by(SiteLabel, Substrate) %>%
    summarise(n_samples = n(), .groups = "drop") %>%
    arrange(Substrate, SiteLabel)
  print(site_summary)
  cat("\n")
  
  # Transpose for vegan (samples as rows)
  community_matrix2 <- t(count_matrix)
  community_matrix<- decostand(community_matrix2, "hel")
  
  return(list(
    community = community_matrix,
    metadata = metadata,
    raw_counts = count_matrix,
    taxonomy = taxonomy
  ))
}


analyze_core_community <- function(count_matrix, metadata, taxonomy, group_name) {
  cat("=== CORE COMMUNITY ANALYSIS:", toupper(group_name), "===\n")
  
  # Convert to relative abundance
  rel_abundance <- sweep(count_matrix, 2, colSums(count_matrix), "/")
  
  # Test different prevalence thresholds
  prevalence_thresholds <- c(0.95, 0.90, 0.80, 0.70, 0.60, 0.50, 0.40, 0.30, 0.20)
  abundance_threshold <- 0.001  # 0.1% relative abundance
  
  core_results <- data.frame(
    Prevalence_Threshold = prevalence_thresholds,
    Core_Taxa = NA,
    Mean_Abundance = NA,
    Total_Abundance = NA
  )
  
  for (i in seq_along(prevalence_thresholds)) {
    threshold <- prevalence_thresholds[i]
    min_samples <- ceiling(threshold * ncol(rel_abundance))
    
    # Find core taxa
    core_taxa <- rowSums(rel_abundance >= abundance_threshold) >= min_samples
    n_core <- sum(core_taxa)
    
    if (n_core > 0) {
      core_abundances <- rel_abundance[core_taxa, , drop = FALSE]
      mean_abundance <- mean(rowMeans(core_abundances))
      total_abundance <- mean(colSums(core_abundances))
    } else {
      mean_abundance <- 0
      total_abundance <- 0
    }
    
    core_results[i, "Core_Taxa"] <- n_core
    core_results[i, "Mean_Abundance"] <- mean_abundance
    core_results[i, "Total_Abundance"] <- total_abundance
  }
  
  # Print results
  cat("Core community analysis results:\n")
  print(core_results)
  
  # Create core community plot
  p_core <- ggplot(core_results, aes(x = Prevalence_Threshold * 100, y = Core_Taxa)) +
    geom_line(size = 1.2, color = "darkblue") +
    geom_point(size = 3, color = "darkred") +
    geom_text(aes(label = Core_Taxa), vjust = -0.5, size = 3) +
    labs(
      title = paste("Core Community Analysis -", toupper(group_name)),
      subtitle = "Number of core taxa at different prevalence thresholds",
      x = "Prevalence Threshold (%)",
      y = "Number of Core Taxa"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12)
    ) +
    scale_x_reverse()
  
  return(list(
    results = core_results,
    plot = p_core
  ))
}



analyze_diversity <- function(community_matrix, metadata, group_name) {
  cat("=== DIVERSITY ANALYSIS:", toupper(group_name), "===\n")
  
  # Calculate diversity indices
  diversity_data <- data.frame(
    Sample = rownames(community_matrix),
    Richness = specnumber(community_matrix),
    Shannon = diversity(community_matrix, index = "shannon"),
    Simpson = diversity(community_matrix, index = "simpson"),
    Evenness = diversity(community_matrix, index = "shannon") / log(specnumber(community_matrix))
  )
  
  # Merge with metadata
  diversity_data <- merge(diversity_data, metadata, by = "Sample")
  
  # Statistical tests
  cat("Alpha diversity comparisons (Wilcoxon tests):\n")
  for (index in c("Richness", "Shannon", "Simpson", "Evenness")) {
    biofilm_vals <- diversity_data[[index]][diversity_data$Substrate == "Biofilm"]
    sediment_vals <- diversity_data[[index]][diversity_data$Substrate == "Sediment"]
    
    test_result <- wilcox.test(biofilm_vals, sediment_vals)
    cat(index, "p-value:", round(test_result$p.value, 4), "\n")
  }
  
  # Create diversity plots
  diversity_long <- diversity_data %>%
    select(Sample, Substrate, SiteLabel, Richness, Shannon, Simpson, Evenness) %>%
    pivot_longer(cols = c(Richness, Shannon, Simpson, Evenness), 
                 names_to = "Index", values_to = "Value")
  
  p_diversity <- ggplot(diversity_long, aes(x = Substrate, y = Value, fill = Substrate)) +
    geom_boxplot(alpha = 0.7) +
    geom_jitter(aes(shape = SiteLabel), width = 0.2, alpha = 0.8, size = 2) +
    facet_wrap(~Index, scales = "free_y") +
    scale_fill_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    scale_shape_manual(values = c(15:25)) +
    labs(title = paste("Alpha Diversity -", toupper(group_name)),
         x = "Substrate", y = "Diversity Value", shape = "Site") +
    theme_minimal() +
    theme(legend.position = "right")
  
  return(list(data = diversity_data, plot = p_diversity))
}

analyze_ordination <- function(community_matrix, metadata, group_name) {
  cat("=== ORDINATION ANALYSIS:", toupper(group_name), "===\n")
  
  # Remove empty samples/species
  community_matrix <- community_matrix[rowSums(community_matrix) > 0, ]
  community_matrix <- community_matrix[, colSums(community_matrix) > 0]
  
  # Calculate distance matrix
  dist_matrix <- vegdist(community_matrix, method = "bray")
  
  # PCoA
  pcoa_result <- pcoa(dist_matrix)
  pcoa_data <- data.frame(
    Sample = rownames(community_matrix),
    PC1 = pcoa_result$vectors[, 1],
    PC2 = pcoa_result$vectors[, 2],
    PC3 = pcoa_result$vectors[, 3]
  )
  
  # Merge with metadata
  pcoa_data <- merge(pcoa_data, metadata, by = "Sample")
  
  # Calculate variance explained
  var_explained <- pcoa_result$values$Relative_eig[1:3] * 100
  
  # PERMANOVA tests
  cat("PERMANOVA results:\n")
  
  # Test substrate effect
  perm_substrate <- adonis2(dist_matrix ~ Substrate, data = metadata, permutations = 999)
  cat("Substrate effect - R2:", round(perm_substrate$R2[1], 4), 
      "p-value:", round(perm_substrate$`Pr(>F)`[1], 4), "\n")
  
  # Test site effect within sediment samples only
  sediment_samples <- metadata$Sample[metadata$Substrate == "Sediment"]
  if (length(sediment_samples) > 0) {
    sediment_metadata <- metadata[metadata$Sample %in% sediment_samples, ]
    sediment_dist <- as.dist(as.matrix(dist_matrix)[sediment_samples, sediment_samples])
    
    perm_site_sediment <- adonis2(sediment_dist ~ SiteLabel, data = sediment_metadata, permutations = 999)
    cat("Site effect (sediment only) - R2:", round(perm_site_sediment$R2[1], 4), 
        "p-value:", round(perm_site_sediment$`Pr(>F)`[1], 4), "\n")
  }
  
  # PERMDISP (test for dispersion differences)
  disp_substrate <- betadisper(dist_matrix, metadata$Substrate)
  disp_test <- permutest(disp_substrate, permutations = 999)
  cat("PERMDISP (dispersion) p-value:", round(disp_test$tab$`Pr(>F)`[1], 4), "\n")
  
  # Create ordination plots
  n_sites <- length(unique(metadata$SiteLabel))
  site_colors <- rainbow(n_sites)
  names(site_colors) <- sort(unique(metadata$SiteLabel))
  
  p_substrate <- ggplot(pcoa_data, aes(x = PC1, y = PC2)) +
    geom_point(aes(color = Substrate, shape = SiteLabel), size = 3, alpha = 0.8) +
    scale_color_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    scale_shape_manual(values = c(15:25)) +
    labs(title = paste("PCoA - Substrate Effect -", toupper(group_name)),
         x = paste0("PC1 (", round(var_explained[1], 1), "%)"),
         y = paste0("PC2 (", round(var_explained[2], 1), "%)"),
         shape = "Site") +
    theme_minimal() +
    stat_ellipse(aes(color = Substrate), level = 0.68, linetype = "dashed") +
    theme(legend.position = "right")
  
  p_site <- ggplot(pcoa_data, aes(x = PC1, y = PC2)) +
    geom_point(aes(color = SiteLabel, shape = Substrate), size = 3, alpha = 0.8) +
    scale_color_manual(values = site_colors) +
    scale_shape_manual(values = c("Biofilm" = 16, "Sediment" = 17)) +
    labs(title = paste("PCoA - Site Effect -", toupper(group_name)),
         x = paste0("PC1 (", round(var_explained[1], 1), "%)"),
         y = paste0("PC2 (", round(var_explained[2], 1), "%)"),
         color = "Site", shape = "Substrate") +
    theme_minimal() +
    theme(legend.position = "right")
  
  return(list(
    pcoa_data = pcoa_data,
    var_explained = var_explained,
    permanova = list(substrate = perm_substrate),
    permdisp = disp_test,
    plots = list(substrate = p_substrate, site = p_site),
    distances = dist_matrix
  ))
}


analyze_dispersion <- function(dist_matrix, metadata, group_name) {
  cat("=== DISPERSION ANALYSIS:", toupper(group_name), "===\n")
  
  # Calculate dispersion
  disp_result <- betadisper(dist_matrix, metadata$Substrate)
  
  # Extract distances to centroid
  disp_data <- data.frame(
    Sample = names(disp_result$distances),
    Distance_to_Centroid = disp_result$distances,
    Substrate = metadata$Substrate[match(names(disp_result$distances), metadata$Sample)],
    SiteLabel = metadata$SiteLabel[match(names(disp_result$distances), metadata$Sample)]
  )
  
  # Statistical test
  disp_test <- permutest(disp_result, permutations = 999)
  cat("Dispersion test p-value:", round(disp_test$tab$`Pr(>F)`[1], 4), "\n")
  
  # Summary statistics
  disp_summary <- disp_data %>%
    group_by(Substrate) %>%
    summarise(
      Mean_Dispersion = mean(Distance_to_Centroid),
      SD_Dispersion = sd(Distance_to_Centroid),
      n = n(),
      .groups = "drop"
    )
  
  print(disp_summary)
  
  # Create dispersion plot
  p_disp <- ggplot(disp_data, aes(x = Substrate, y = Distance_to_Centroid, fill = Substrate)) +
    geom_boxplot(alpha = 0.7) +
    geom_jitter(aes(shape = SiteLabel), width = 0.2, alpha = 0.8, size = 2) +
    scale_fill_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    scale_shape_manual(values = c(15:25)) +
    labs(title = paste("Community Dispersion -", toupper(group_name)),
         x = "Substrate", y = "Distance to Centroid", shape = "Site") +
    theme_minimal() +
    theme(legend.position = "right")
  
  return(list(data = disp_data, summary = disp_summary, plot = p_disp, test = disp_test))
}


analyze_differential_abundance <- function(count_matrix, metadata, taxonomy, group_name) {
  cat("=== DIFFERENTIAL ABUNDANCE ANALYSIS:", toupper(group_name), "===\n")

  # Find genus column in taxonomy
  genus_col <- which(grepl("genus|Genus", colnames(taxonomy), ignore.case = TRUE))
  if (length(genus_col) == 0) {
    genus_col <- min(6, ncol(taxonomy))
  }

  # Match OTUs with taxonomy
  common_otus <- intersect(rownames(count_matrix), taxonomy[[1]])
  count_matrix <- count_matrix[common_otus, ]
  taxonomy <- taxonomy[taxonomy[[1]] %in% common_otus, ]
  taxonomy <- taxonomy[match(rownames(count_matrix), taxonomy[[1]]), ]

  # Extract genus information
  genus_info <- taxonomy[[genus_col]]
  genus_info <- gsub("g__", "", genus_info)
  genus_info <- gsub("^$|^NA$", "Unknown", genus_info)
  genus_info[is.na(genus_info)] <- "Unknown"

  # Aggregate to genus level
  genus_counts <- aggregate(count_matrix, by = list(Genus = genus_info), FUN = sum)
  rownames(genus_counts) <- genus_counts$Genus
  genus_counts <- genus_counts[, -1]

  # Convert to relative abundance
  genus_rel <- sweep(genus_counts, 2, colSums(genus_counts), "/")

  # Filter genera (present in at least 10% of samples with >0.1% relative abundance)
  min_prevalence <- 0.1
  min_abundance <- 0.001
  min_samples <- ceiling(min_prevalence * ncol(genus_rel))
  keep <- rowSums(genus_rel >= min_abundance) >= min_samples
  genus_rel_filt <- genus_rel[keep, ]

  cat("After filtering:", nrow(genus_rel_filt), "genera retained\n")

  # Prepare results dataframe
  results <- data.frame(
    Genus = rownames(genus_rel_filt),
    Mean_Biofilm = NA,
    Mean_Sediment = NA,
    Log2FC = NA,
    P_value = NA,
    P_adjusted = NA,
    Significant = FALSE,
    stringsAsFactors = FALSE
  )

  # Get substrate groups
  biofilm_samples <- metadata$Sample[metadata$Substrate == "Biofilm"]
  sediment_samples <- metadata$Sample[metadata$Substrate == "Sediment"]

  cat("Testing", nrow(genus_rel_filt), "genera...\n")

  # Perform tests for each genus
  for (i in 1:nrow(genus_rel_filt)) {
    # Get abundance data and convert to numeric
    biofilm_abund <- as.numeric(genus_rel_filt[i, biofilm_samples])
    sediment_abund <- as.numeric(genus_rel_filt[i, sediment_samples])

    # Calculate means
    mean_biofilm <- mean(biofilm_abund, na.rm = TRUE)
    mean_sediment <- mean(sediment_abund, na.rm = TRUE)

    # Calculate log2 fold change
    log2fc <- log2((mean_biofilm + 1e-6) / (mean_sediment + 1e-6))

    # Perform Wilcoxon test
    if (length(biofilm_abund) > 1 && length(sediment_abund) > 1) {
      test_result <- wilcox.test(biofilm_abund, sediment_abund)
      p_value <- test_result$p.value
    } else {
      p_value <- NA
    }

    # Store results
    results[i, "Mean_Biofilm"] <- mean_biofilm
    results[i, "Mean_Sediment"] <- mean_sediment
    results[i, "Log2FC"] <- log2fc
    results[i, "P_value"] <- p_value
  }

  # Adjust p-values
  results$P_adjusted <- p.adjust(results$P_value, method = "BH")
  results$Significant <- !is.na(results$P_adjusted) & results$P_adjusted < 0.05

  # Sort by adjusted p-value
  results <- results[order(results$P_adjusted, na.last = TRUE), ]

  # Summary
  n_significant <- sum(results$Significant, na.rm = TRUE)
  n_enriched_biofilm <- sum(results$Significant & results$Log2FC > 0, na.rm = TRUE)
  n_enriched_sediment <- sum(results$Significant & results$Log2FC < 0, na.rm = TRUE)

  cat("\nResults Summary:\n")
  cat("- Total genera tested:", nrow(results), "\n")
  cat("- Significant genera (padj < 0.05):", n_significant, "\n")
  cat("- Enriched in Biofilm:", n_enriched_biofilm, "\n")
  cat("- Enriched in Sediment:", n_enriched_sediment, "\n")

  # Create volcano plot
  plot_data <- results[is.finite(results$Log2FC) & !is.na(results$P_adjusted), ]

  p_volcano <- ggplot(plot_data, aes(x = Log2FC, y = -log10(P_adjusted))) +
    geom_point(aes(color = Significant), alpha = 0.6, size = 2) +
    scale_color_manual(values = c("FALSE" = "gray60", "TRUE" = "red")) +
    geom_hline(yintercept = -log10(0.05), linetype = "dashed", color = "blue") +
    geom_vline(xintercept = 0, linetype = "dashed", color = "gray30") +
    labs(
      title = paste("Differential Abundance Analysis -", toupper(group_name)),
      subtitle = "Biofilm vs Sediment (Wilcoxon Test)",
      x = "Log2 Fold Change (Biofilm/Sediment)",
      y = "-Log10 Adjusted P-value"
    ) +
    theme_minimal() +
    theme(
      legend.position = "none",
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 12)
    )

  # Add labels for significant genera if any
  sig_data <- plot_data[plot_data$Significant, ]
  if (nrow(sig_data) > 0) {
    top_sig <- head(sig_data, 15)
    p_volcano <- p_volcano + geom_text_repel(
      data = top_sig,
      aes(label = Genus),
      size = 3,
      max.overlaps = 15,
      box.padding = 0.3,
      point.padding = 0.3
    )
  }

  return(list(
    results = results,
    plot = p_volcano
  ))
}


comprehensive_analysis <- function(group_name) {
  cat("\n", paste(rep("=", 70), collapse = ""), "\n")
  cat("COMPREHENSIVE ANALYSIS:", toupper(group_name), "\n")
  cat(paste(rep("=", 70), collapse = ""), "\n")

  # Prepare data
  data <- prepare_data(group_name)

  # Run all analyses
  core_results <- analyze_core_community(data$raw_counts, data$metadata, data$taxonomy, group_name)
  diversity_results <- analyze_diversity(data$community, data$metadata, group_name)
  ordination_results <- analyze_ordination(data$community, data$metadata, group_name)
  dispersion_results <- analyze_dispersion(ordination_results$distances, data$metadata, group_name)
  diff_abundance_results <- analyze_differential_abundance(data$raw_counts, data$metadata, data$taxonomy, group_name)

  # Create comprehensive 4-panel figure
  combined_plot <- grid.arrange(
    diversity_results$plot,
    ordination_results$plots$substrate,
    ordination_results$plots$site,
    dispersion_results$plot,
    ncol = 2, nrow = 2
  )

  # Save all results
  ggsave(paste0("comprehensive_figure_", group_name, ".png"), combined_plot,
         width = 16, height = 12, dpi = 300)

  ggsave(paste0("core_community_", group_name, ".png"), core_results$plot,
         width = 10, height = 6, dpi = 300)

  ggsave(paste0("volcano_plot_", group_name, ".png"), diff_abundance_results$plot,
         width = 10, height = 8, dpi = 300)

  ggsave(paste0("pcoa_substrate_", group_name, ".png"), ordination_results$plots$substrate,
         width = 12, height = 8, dpi = 300)

  ggsave(paste0("pcoa_site_", group_name, ".png"), ordination_results$plots$site,
         width = 12, height = 8, dpi = 300)

  # Save data tables
  write.csv(core_results$results, paste0("core_community_results_", group_name, ".csv"), row.names = FALSE)
  write.csv(diff_abundance_results$results, paste0("differential_abundance_", group_name, ".csv"), row.names = FALSE)

  cat("\nFiles saved for", toupper(group_name), ":\n")
  cat("- comprehensive_figure_", group_name, ".png (4-panel main figure)\n")
  cat("- core_community_", group_name, ".png\n")
  cat("- volcano_plot_", group_name, ".png\n")
  cat("- pcoa_substrate_", group_name, ".png\n")
  cat("- pcoa_site_", group_name, ".png\n")
  cat("- core_community_results_", group_name, ".csv\n")
  cat("- differential_abundance_", group_name, ".csv\n")

  return(list(
    core = core_results,
    diversity = diversity_results,
    ordination = ordination_results,
    dispersion = dispersion_results,
    differential = diff_abundance_results,
    data = data
  ))
}



create_summary_table <- function(bacteria_results, fungi_results, archaea_results) {
  summary_data <- data.frame(
    Group = c("Bacteria", "Fungi", "Archaea"),

    # Sample sizes
    Total_Samples = c(
      nrow(bacteria_results$diversity$data),
      nrow(fungi_results$diversity$data),
      nrow(archaea_results$diversity$data)
    ),

    Biofilm_Samples = c(
      sum(bacteria_results$diversity$data$Substrate == "Biofilm"),
      sum(fungi_results$diversity$data$Substrate == "Biofilm"),
      sum(archaea_results$diversity$data$Substrate == "Biofilm")
    ),

    Sediment_Samples = c(
      sum(bacteria_results$diversity$data$Substrate == "Sediment"),
      sum(fungi_results$diversity$data$Substrate == "Sediment"),
      sum(archaea_results$diversity$data$Substrate == "Sediment")
    ),

    Biofilm_Sites = c(2, 2, 2),
    Sediment_Sites = c(7, 7, 7),

    # PERMANOVA results
    Substrate_R2 = c(
      round(bacteria_results$ordination$permanova$substrate$R2[1], 4),
      round(fungi_results$ordination$permanova$substrate$R2[1], 4),
      round(archaea_results$ordination$permanova$substrate$R2[1], 4)
    ),

    Substrate_pvalue = c(
      round(bacteria_results$ordination$permanova$substrate$`Pr(>F)`[1], 4),
      round(fungi_results$ordination$permanova$substrate$`Pr(>F)`[1], 4),
      round(archaea_results$ordination$permanova$substrate$`Pr(>F)`[1], 4)
    ),

    # Dispersion test
    Dispersion_pvalue = c(
      round(bacteria_results$dispersion$test$tab$`Pr(>F)`[1], 4),
      round(fungi_results$dispersion$test$tab$`Pr(>F)`[1], 4),
      round(archaea_results$dispersion$test$tab$`Pr(>F)`[1], 4)
    ),

    # Core community at 20% prevalence
    Core_Taxa_20pct = c(
      bacteria_results$core$results$Core_Taxa[bacteria_results$core$results$Prevalence_Threshold == 0.2],
      fungi_results$core$results$Core_Taxa[fungi_results$core$results$Prevalence_Threshold == 0.2],
      archaea_results$core$results$Core_Taxa[archaea_results$core$results$Prevalence_Threshold == 0.2]
    ),

    # Differential abundance
    Significant_Genera = c(
      sum(bacteria_results$differential$results$Significant, na.rm = TRUE),
      sum(fungi_results$differential$results$Significant, na.rm = TRUE),
      sum(archaea_results$differential$results$Significant, na.rm = TRUE)
    ),

    stringsAsFactors = FALSE
  )

  return(summary_data)
}

create_final_summary_plots <- function(summary_data) {
  # Substrate effects plot
  summary_long <- summary_data %>%
    mutate(
      Substrate_Significant = ifelse(Substrate_pvalue < 0.05, "Significant", "Not Significant"),
      Substrate_Level = case_when(
        Substrate_pvalue < 0.001 ~ "p < 0.001",
        Substrate_pvalue < 0.01 ~ "p < 0.01",
        Substrate_pvalue < 0.05 ~ "p < 0.05",
        Substrate_pvalue < 0.1 ~ "p < 0.1",
        TRUE ~ "p ≥ 0.1"
      )
    )

  p1 <- ggplot(summary_long, aes(x = Group, y = Substrate_R2 * 100, fill = Substrate_Significant)) +
    geom_col(alpha = 0.8, width = 0.7) +
    geom_text(aes(label = paste0(round(Substrate_R2 * 100, 2), "%\n", Substrate_Level)),
              vjust = -0.5, size = 3.5, fontface = "bold") +
    scale_fill_manual(values = c("Significant" = "darkgreen", "Not Significant" = "gray60")) +
    labs(
      title = "Substrate Effects Across Microbial Groups",
      subtitle = "PERMANOVA results: Independent biofilm vs sediment sites",
      x = "Microbial Group",
      y = "Variance Explained by Substrate (%)",
      fill = "Statistical Significance"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 11),
      legend.position = "bottom",
      axis.text.x = element_text(size = 12, face = "bold")
    ) +
    ylim(0, max(summary_long$Substrate_R2 * 100) * 1.4)

  # Core community plot
  p2 <- ggplot(summary_data, aes(x = Group, y = Core_Taxa_20pct)) +
    geom_col(fill = "steelblue", alpha = 0.7, width = 0.7) +
    geom_text(aes(label = Core_Taxa_20pct), vjust = -0.5, size = 4, fontface = "bold") +
    labs(
      title = "Core Community Size (20% Prevalence Threshold)",
      subtitle = "Number of core taxa present in ≥20% of samples",
      x = "Microbial Group",
      y = "Number of Core Taxa"
    ) +
    theme_minimal() +
    theme(
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 11),
      axis.text.x = element_text(size = 12, face = "bold")
    )

  # Study design plot
  design_data <- data.frame(
    Site = c("BF1", "BF2", "SED1", "SED2", "SED3", "SED4", "SED5", "SED6", "SED7"),
    Substrate = c("Biofilm", "Biofilm", rep("Sediment", 7)),
    Samples = c(6, 6, 6, 6, 6, 6, 6, 6, 6),
    x = c(1, 2, 4, 5, 6, 7, 8, 9, 10),
    y = c(2, 2, 1, 1, 1, 1, 1, 1, 1)
  )

  p3 <- ggplot(design_data, aes(x = x, y = y)) +
    geom_point(aes(color = Substrate, size = Samples), alpha = 0.8) +
    geom_text(aes(label = Site), vjust = -1.5, size = 3, fontface = "bold") +
    geom_text(aes(label = paste(Samples, "samples")), vjust = 2, size = 2.5) +
    scale_color_manual(values = c("Biofilm" = "darkblue", "Sediment" = "darkred")) +
    scale_size_continuous(range = c(8, 12), guide = "none") +
    labs(
      title = "Study Design - Independent Site Structure",
      subtitle = "Biofilm and sediment samples from completely different locations",
      x = "", y = "", color = "Substrate Type"
    ) +
    theme_minimal() +
    theme(
      axis.text = element_blank(), axis.ticks = element_blank(), panel.grid = element_blank(),
      plot.title = element_text(size = 14, face = "bold"),
      plot.subtitle = element_text(size = 11), legend.position = "bottom"
    ) +
    annotate("text", x = 1.5, y = 2.5, label = "Biofilm Sites\n(Independent locations)",
             size = 3, fontface = "italic", color = "darkblue") +
    annotate("text", x = 7, y = 0.5, label = "Sediment Sites\n(Independent locations)",
             size = 3, fontface = "italic", color = "darkred")

  return(list(substrate = p1, core = p2, design = p3))
}




# Run comprehensive analysis for all groups
bacteria_results <- comprehensive_analysis("bacteria")
fungi_results <- comprehensive_analysis("fungi")
archaea_results <- comprehensive_analysis("archaea")

# Create summary table and plots
summary_table <- create_summary_table(bacteria_results, fungi_results, archaea_results)
summary_plots <- create_final_summary_plots(summary_table)

# Save summary materials
write.csv(summary_table, "comprehensive_summary_table.csv", row.names = FALSE)

# Create and save final combined summary
final_summary <- grid.arrange(
  summary_plots$substrate,
  summary_plots$core,
  summary_plots$design,
  ncol = 1,
  top = textGrob("Comprehensive Analysis Summary: Domain-Specific Responses",
                 gp = gpar(fontsize = 16, fontface = "bold"))
)

ggsave("comprehensive_analysis_summary.png", final_summary,
       width = 12, height = 15, dpi = 300)

# Print final summary
cat("\n", paste(rep("=", 70), collapse = ""), "\n")
cat("COMPREHENSIVE ANALYSIS COMPLETE\n")
cat(paste(rep("=", 70), collapse = ""), "\n")

print(summary_table)

# Create interpretation summary
interpretation <- data.frame(
  Group = c("Bacteria", "Fungi", "Archaea"),
  Substrate_Effect = ifelse(summary_table$Substrate_pvalue < 0.05,
                           paste("SIGNIFICANT (p =", summary_table$Substrate_pvalue, ")"),
                           paste("Not significant (p =", summary_table$Substrate_pvalue, ")")),
  Variance_Explained = paste0(round(summary_table$Substrate_R2 * 100, 2), "%"),
  Core_Community = paste(summary_table$Core_Taxa_20pct, "taxa at 20% prevalence"),
  Interpretation = c(
    "Substrate specialist - responds to biofilm vs sediment microenvironments",
    "Marginal substrate response - primarily driven by site-level factors",
    "Substrate independent - stochastic assembly processes dominate"
  )
)

write.csv(interpretation, "final_interpretation_summary.csv", row.names = FALSE)

cat("\n=== FINAL INTERPRETATION ===\n")
print(interpretation)

cat("\n=== ALL FILES GENERATED ===\n")
cat("Main figures (per group):\n")
cat("- comprehensive_figure_bacteria.png (4-panel: diversity + PCoA + dispersion)\n")
cat("- comprehensive_figure_fungi.png\n")
cat("- comprehensive_figure_archaea.png\n")
cat("\nSupplementary figures (per group):\n")
cat("- core_community_*.png\n")
cat("- volcano_plot_*.png\n")
cat("- pcoa_substrate_*.png\n")
cat("- pcoa_site_*.png\n")
cat("\nSummary figures:\n")
cat("- comprehensive_analysis_summary.png (combined summary)\n")
cat("\nData tables:\n")
cat("- comprehensive_summary_table.csv (statistical summary)\n")
cat("- final_interpretation_summary.csv (biological interpretation)\n")
cat("- core_community_results_*.csv (per group)\n")
cat("- differential_abundance_*.csv (per group)\n")

cat("\n🎯 ANALYSIS COMPLETE! 🎯\n")
cat("All publication-ready materials generated successfully.\n")
