# Diagnostic Script for Barplot Issues
# Check why some sites don't reach 100%

library(readxl)
library(dplyr)

# Function to diagnose a specific group and taxonomic level
diagnose_group <- function(group_name, tax_level) {
  cat("=== DIAGNOSING:", toupper(group_name), tax_level, "===\n")
  
  # Read data
  otu_table <- read_excel(paste0("OTU_table_", group_name, ".xlsx"))
  metadata <- read_excel(paste0("metadata_", group_name, ".xlsx"))
  taxonomy <- read_excel(paste0("taxonomy_", group_name, ".xlsx"))
  
  # Prepare matrices
  count_matrix <- as.matrix(otu_table[, -1])
  rownames(count_matrix) <- otu_table[[1]]
  
  # Match samples
  common_samples <- intersect(colnames(count_matrix), metadata$Sample)
  count_matrix <- count_matrix[, common_samples]
  metadata <- metadata[metadata$Sample %in% common_samples, ]
  metadata <- metadata[match(colnames(count_matrix), metadata$Sample), ]
  
  # Match taxonomy
  common_otus <- intersect(rownames(count_matrix), taxonomy[[1]])
  count_matrix <- count_matrix[common_otus, ]
  taxonomy <- taxonomy[taxonomy[[1]] %in% common_otus, ]
  taxonomy <- taxonomy[match(rownames(count_matrix), taxonomy[[1]]), ]
  
  # Create site labels
  metadata$SiteNumber <- gsub("Site([0-9]+).*", "\\1", metadata$Site)
  metadata$SiteLabel <- ifelse(
    metadata$Substrate == "Biofilm",
    paste0("BF", metadata$SiteNumber),
    paste0("SED", metadata$SiteNumber)
  )
  
  # Convert to relative abundance
  rel_abundance <- sweep(count_matrix, 2, colSums(count_matrix), "/") * 100
  
  # Get taxonomy column
  if (tax_level == "Class") {
    tax_col <- "Class"
  } else if (tax_level == "Family") {
    tax_col <- "Family"
  }
  
  # Handle missing taxonomy
  tax_vector <- taxonomy[[tax_col]]
  tax_vector[is.na(tax_vector) | tax_vector == "" | tax_vector == "unidentified"] <- "Unassigned"
  
  # Aggregate by taxonomy
  tax_abundance <- data.frame(
    Taxonomy = tax_vector,
    rel_abundance,
    stringsAsFactors = FALSE
  )
  
  # Sum by taxonomy
  tax_summary <- tax_abundance %>%
    group_by(Taxonomy) %>%
    summarise(across(everything(), sum), .groups = 'drop')
  
  # Convert back to matrix
  tax_matrix <- as.matrix(tax_summary[, -1])
  rownames(tax_matrix) <- tax_summary$Taxonomy
  
  # Check column sums before site aggregation
  cat("Column sums before site aggregation:\n")
  col_sums <- colSums(tax_matrix)
  for (i in 1:min(10, length(col_sums))) {
    cat(names(col_sums)[i], ":", round(col_sums[i], 2), "%\n")
  }
  
  # Aggregate by site (mean of replicates)
  site_abundance <- data.frame()
  
  for (site in unique(metadata$SiteLabel)) {
    site_samples <- metadata$Sample[metadata$SiteLabel == site]
    site_indices <- match(site_samples, colnames(tax_matrix))
    
    cat("\nSite", site, "- samples:", length(site_samples), "\n")
    cat("Sample names:", paste(site_samples, collapse = ", "), "\n")
    
    if (length(site_indices) > 1) {
      site_mean <- rowMeans(tax_matrix[, site_indices, drop = FALSE])
      cat("Using mean of", length(site_indices), "samples\n")
    } else {
      site_mean <- tax_matrix[, site_indices]
      cat("Using single sample\n")
    }
    
    site_total <- sum(site_mean)
    cat("Site total before grouping:", round(site_total, 4), "%\n")
    
    site_data <- data.frame(
      Site = site,
      Substrate = metadata$Substrate[metadata$SiteLabel == site][1],
      Taxonomy = names(site_mean),
      Abundance = as.numeric(site_mean),
      stringsAsFactors = FALSE
    )
    
    site_abundance <- rbind(site_abundance, site_data)
  }
  
  # Calculate total abundance per taxonomy across all sites
  tax_totals <- site_abundance %>%
    group_by(Taxonomy) %>%
    summarise(Total = sum(Abundance), .groups = 'drop') %>%
    arrange(desc(Total))
  
  # Get top taxa (excluding Unassigned for ranking)
  top_taxa <- tax_totals %>%
    filter(Taxonomy != "Unassigned") %>%
    head(19) %>%  # Top 19 + Others + Unassigned = 21
    pull(Taxonomy)
  
  # Create "Others" category
  site_abundance_final <- site_abundance %>%
    mutate(
      Taxonomy_grouped = case_when(
        Taxonomy == "Unassigned" ~ "Unassigned",
        Taxonomy %in% top_taxa ~ Taxonomy,
        TRUE ~ "Others"
      )
    ) %>%
    group_by(Site, Substrate, Taxonomy_grouped) %>%
    summarise(Abundance = sum(Abundance), .groups = 'drop') %>%
    rename(Taxonomy = Taxonomy_grouped)
  
  # Check totals after grouping
  cat("\n=== TOTALS AFTER GROUPING ===\n")
  site_totals_after_grouping <- site_abundance_final %>%
    group_by(Site) %>%
    summarise(Total = sum(Abundance), .groups = 'drop')
  
  for (i in 1:nrow(site_totals_after_grouping)) {
    total <- round(site_totals_after_grouping$Total[i], 4)
    cat("Site", site_totals_after_grouping$Site[i], "total:", total, "%\n")
  }
  
  return(site_abundance_final)
}

# Diagnose the problematic cases
cat("DIAGNOSTIC ANALYSIS FOR BARPLOT ISSUES\n")
cat("=====================================\n\n")

# Check bacteria classes (SED3 issue)
bacteria_class_data <- diagnose_group("bacteria", "Class")

cat("\n" , paste(rep("=", 60), collapse = ""), "\n\n")

# Check archaea families (SED1 issue)
archaea_family_data <- diagnose_group("archaea", "Family")

cat("\n=== DIAGNOSIS COMPLETE ===\n")
